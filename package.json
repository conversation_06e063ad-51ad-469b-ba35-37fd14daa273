{"name": "city-insights-ai", "version": "1.0.0", "description": "AI-powered urban analytics platform using MongoDB vector search and Gemini AI", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"install:all": "npm install && npm install --prefix frontend && npm install --prefix backend", "dev": "concurrently \"npm run dev --prefix backend\" \"npm run dev --prefix frontend\"", "dev:backend": "npm run dev --prefix backend", "dev:frontend": "npm run dev --prefix frontend", "build": "npm run build --prefix frontend && npm run build --prefix backend", "build:frontend": "npm run build --prefix frontend", "start": "npm start --prefix backend", "test": "npm test --prefix backend && npm test --prefix frontend", "lint": "npm run lint --prefix backend && npm run lint --prefix frontend", "lint:fix": "npm run lint:fix --prefix backend && npm run lint:fix --prefix frontend", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules frontend/dist backend/dist", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down", "data:import": "npm run data:import --prefix backend", "data:csv": "npm run data:csv --prefix backend", "data:ingest": "npm run data:ingest --prefix backend", "data:ingest:full": "npm run data:ingest:full --prefix backend", "setup:vector": "npm run setup:vector --prefix backend", "setup:embeddings": "npm run setup:embeddings --prefix backend", "setup:complete": "npm run data:import && npm run setup:vector && npm run setup:embeddings"}, "keywords": ["urban-analytics", "ai", "mongodb", "vector-search", "gemini", "neighborhoods", "hackathon", "google-cloud"], "author": "City Insights AI Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/city-insights-ai.git"}, "bugs": {"url": "https://github.com/your-username/city-insights-ai/issues"}, "homepage": "https://github.com/your-username/city-insights-ai#readme"}