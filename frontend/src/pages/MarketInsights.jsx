import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Paper
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  Star as StarIcon,
  AttachMoney as MoneyIcon,
  Home as HomeIcon
} from '@mui/icons-material'
import { Helmet } from 'react-helmet-async'
import { useQuery } from 'react-query'
import ReactMarkdown from 'react-markdown'
import { neighborhoodAPI } from '../services/api'

const MarketInsights = () => {

  // Fetch market insights
  const { data: marketData, isLoading, error } = useQuery(
    'market-insights',
    neighborhoodAPI.getMarketInsights,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  )

  // Extract the actual insights text from the response
  const insightsText = marketData?.aiAnalysis?.insights ||
                      marketData?.data?.aiAnalysis?.insights ||
                      marketData?.data?.insights ||
                      marketData?.insights

  // Ensure we have a string for ReactMarkdown
  const insights = typeof insightsText === 'string' ? insightsText :
                  typeof insightsText === 'object' && insightsText?.insights ? insightsText.insights :
                  'No detailed analysis available.'
  const dataPoints = marketData?.data?.dataPoints || marketData?.overview?.totalDataPoints
  const comprehensiveData = marketData?.data || marketData



  // Extract market data from comprehensive response
  const marketDataStats = comprehensiveData?.marketData || {
    averageRent: comprehensiveData?.rentals?.averagePrice || comprehensiveData?.neighborhoods?.averageRent,
    averageSafety: comprehensiveData?.crime?.overallSafetyScore ||
      (comprehensiveData?.neighborhoods?.safetyStats ?
        Object.keys(comprehensiveData.neighborhoods.safetyStats).reduce((sum, key) =>
          sum + (parseInt(key) * comprehensiveData.neighborhoods.safetyStats[key]), 0) /
        Object.values(comprehensiveData.neighborhoods.safetyStats).reduce((sum, count) => sum + count, 0) : null),
    rentRange: {
      min: comprehensiveData?.rentals?.locationStats?.reduce((min, loc) => Math.min(min, loc.avgPrice), Infinity) || 0,
      max: comprehensiveData?.rentals?.locationStats?.reduce((max, loc) => Math.max(max, loc.avgPrice), 0) || 0
    },
    boroughStats: comprehensiveData?.neighborhoods?.boroughStats || []
  }

  // Extract trends from comprehensive data
  const trendsData = comprehensiveData?.trends ||
                    comprehensiveData?.aiAnalysis?.trends ||
                    marketData?.trends || []
  const recommendationsData = comprehensiveData?.recommendations ||
                             comprehensiveData?.aiAnalysis?.recommendations ||
                             marketData?.recommendations || []

  const formatCurrency = (amount) => {
    return `R${amount?.toLocaleString() || 'N/A'}`
  }

  const getTrendIcon = (type) => {
    switch (type) {
      case 'hot':
        return <TrendingUpIcon color="error" />
      case 'value':
        return <MoneyIcon color="success" />
      case 'luxury':
        return <StarIcon color="warning" />
      default:
        return <HomeIcon color="primary" />
    }
  }

  const getTrendColor = (type) => {
    switch (type) {
      case 'hot':
        return 'error'
      case 'value':
        return 'success'
      case 'luxury':
        return 'warning'
      default:
        return 'primary'
    }
  }

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load market insights. Please try again later.
        </Alert>
      </Box>
    )
  }

  return (
    <>
      <Helmet>
        <title>Market Insights - City Insights AI</title>
        <meta name="description" content="AI-powered Cape Town real estate market insights and trends" />
      </Helmet>

      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom fontWeight={600}>
          🏠 Cape Town Market Insights
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          AI-powered analysis of Cape Town's rental market trends and opportunities
        </Typography>

        {/* Comprehensive Data Overview */}
        {comprehensiveData && (comprehensiveData.education || comprehensiveData.healthcare || comprehensiveData.rentals || comprehensiveData.transport || comprehensiveData.crime) && (
          <>
            <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
              📊 Comprehensive Data Overview
            </Typography>
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {/* Education Stats */}
              {comprehensiveData.education && (
                <Grid item xs={12} md={6} lg={3}>
                  <Card sx={{
                    background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%)',
                    border: 1,
                    borderColor: 'success.light',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 3 }
                  }}>
                    <CardContent sx={{ textAlign: 'center', py: 3 }}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                        🎓 Education
                      </Typography>
                      <Typography variant="h4" color="success.main" gutterBottom fontWeight={700}>
                        {comprehensiveData.education.total}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Public Schools
                      </Typography>
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        {comprehensiveData.education.districtBreakdown?.length} Districts
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              )}

              {/* Healthcare Stats */}
              {comprehensiveData.healthcare && (
                <Grid item xs={12} md={6} lg={3}>
                  <Card sx={{
                    background: 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(244, 67, 54, 0.05) 100%)',
                    border: 1,
                    borderColor: 'error.light',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 3 }
                  }}>
                    <CardContent sx={{ textAlign: 'center', py: 3 }}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                        🏥 Healthcare
                      </Typography>
                      <Typography variant="h4" color="error.main" gutterBottom fontWeight={700}>
                        {comprehensiveData.healthcare.total}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Medical Facilities
                      </Typography>
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        {comprehensiveData.healthcare.classificationBreakdown?.length} Types
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              )}

              {/* Rental Stats */}
              {comprehensiveData.rentals && (
                <Grid item xs={12} md={6} lg={3}>
                  <Card sx={{
                    background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(25, 118, 210, 0.05) 100%)',
                    border: 1,
                    borderColor: 'primary.light',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 3 }
                  }}>
                    <CardContent sx={{ textAlign: 'center', py: 3 }}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                        🏠 Rentals
                      </Typography>
                      <Typography variant="h4" color="primary.main" gutterBottom fontWeight={700}>
                        {comprehensiveData.rentals.totalProperties}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Properties
                      </Typography>
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        {comprehensiveData.rentals.occupancyRate}% Unoccupied
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              )}

              {/* Transport Stats */}
              {comprehensiveData.transport && (
                <Grid item xs={12} md={6} lg={3}>
                  <Card sx={{
                    background: 'linear-gradient(135deg, rgba(255, 152, 0, 0.1) 0%, rgba(255, 152, 0, 0.05) 100%)',
                    border: 1,
                    borderColor: 'warning.light',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 3 }
                  }}>
                    <CardContent sx={{ textAlign: 'center', py: 3 }}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                        🚌 Transport
                      </Typography>
                      <Typography variant="h4" color="warning.main" gutterBottom fontWeight={700}>
                        {comprehensiveData.transport.totalRoutes}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Taxi Routes
                      </Typography>
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        {comprehensiveData.transport.connectivity} Connections
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              )}

              {/* Crime/Safety Stats */}
              {comprehensiveData.crime && (
                <Grid item xs={12} md={6} lg={3}>
                  <Card sx={{
                    background: 'linear-gradient(135deg, rgba(156, 39, 176, 0.1) 0%, rgba(156, 39, 176, 0.05) 100%)',
                    border: 1,
                    borderColor: 'secondary.light',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': { transform: 'translateY(-2px)', boxShadow: 3 }
                  }}>
                    <CardContent sx={{ textAlign: 'center', py: 3 }}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                        🛡️ Safety
                      </Typography>
                      <Typography variant="h4" color="secondary.main" gutterBottom fontWeight={700}>
                        {comprehensiveData.crime.overallSafetyScore}/10
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Safety Score
                      </Typography>
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        {comprehensiveData.crime.totalCrimes} Incidents Analyzed
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>
          </>
        )}

        <Grid container spacing={3}>
          {/* Market Overview Cards */}
          <Grid item xs={12} md={4}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(25, 118, 210, 0.05) 100%)',
              border: 1,
              borderColor: 'primary.light',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 3
              }
            }}>
              <CardContent sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="h6" gutterBottom color="primary" sx={{ fontWeight: 600, mb: 2 }}>
                  💰 Average Rent
                </Typography>
                <Typography variant="h3" fontWeight={700} color="primary.main" sx={{ mb: 1 }}>
                  {formatCurrency(marketDataStats?.averageRent)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Across {comprehensiveData?.neighborhoods?.totalNeighborhoods || 0} neighborhoods
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%)',
              border: 1,
              borderColor: 'success.light',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 3
              }
            }}>
              <CardContent sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="h6" gutterBottom color="success.main" sx={{ fontWeight: 600, mb: 2 }}>
                  🛡️ Average Safety
                </Typography>
                <Typography variant="h3" fontWeight={700} color="success.main" sx={{ mb: 1 }}>
                  {marketDataStats?.averageSafety ? marketDataStats.averageSafety.toFixed(1) : 'N/A'}/10
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  City-wide safety score
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(255, 152, 0, 0.1) 0%, rgba(255, 152, 0, 0.05) 100%)',
              border: 1,
              borderColor: 'warning.light',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: 3
              }
            }}>
              <CardContent sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="h6" gutterBottom color="warning.main" sx={{ fontWeight: 600, mb: 2 }}>
                  📊 Price Range
                </Typography>
                <Typography variant="h5" fontWeight={700} color="warning.main" sx={{ mb: 1 }}>
                  {formatCurrency(marketDataStats?.rentRange?.min)} - {formatCurrency(marketDataStats?.rentRange?.max)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Market spread
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Trending Insights */}
          <Grid item xs={12} md={6}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(255, 152, 0, 0.05) 0%, rgba(255, 152, 0, 0.02) 100%)',
              border: 1,
              borderColor: 'warning.light'
            }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  color: 'warning.main',
                  fontWeight: 600
                }}>
                  🔥 Trending Now
                </Typography>
                {trendsData?.length > 0 ? trendsData.map((trend, index) => (
                  <Paper
                    key={index}
                    sx={{
                      mb: 2,
                      p: 2.5,
                      backgroundColor: 'rgba(255,255,255,0.8)',
                      borderRadius: 2,
                      border: 1,
                      borderColor: 'divider',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-1px)',
                        boxShadow: 2
                      }
                    }}
                  >
                    <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.5 }}>
                      {typeof trend === 'string' ? trend : trend.description || trend}
                    </Typography>
                  </Paper>
                )) : (
                  <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                    Analyzing market trends from comprehensive data...
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Market Recommendations */}
          <Grid item xs={12} md={6}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(76, 175, 80, 0.02) 100%)',
              border: 1,
              borderColor: 'success.light'
            }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  color: 'success.main',
                  fontWeight: 600
                }}>
                  💡 AI Recommendations
                </Typography>
                {recommendationsData?.length > 0 ? recommendationsData.map((rec, index) => (
                  <Paper
                    key={index}
                    sx={{
                      mb: 2,
                      p: 2.5,
                      backgroundColor: 'rgba(255,255,255,0.8)',
                      borderRadius: 2,
                      border: 1,
                      borderColor: 'divider',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-1px)',
                        boxShadow: 2
                      }
                    }}
                  >
                    <Typography variant="subtitle2" fontWeight={600} color="success.dark" sx={{ mb: 1 }}>
                      {rec.type || rec.category || 'Recommendation'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1.5, lineHeight: 1.5 }}>
                      {rec.title || rec.recommendation || rec.description}
                    </Typography>
                    {rec.areas && (
                      <Typography variant="caption" color="text.secondary" sx={{
                        display: 'block',
                        padding: 1,
                        backgroundColor: 'grey.50',
                        borderRadius: 1,
                        fontStyle: 'italic'
                      }}>
                        Areas: {Array.isArray(rec.areas) ? rec.areas.join(', ') : rec.areas} • {rec.reason || rec.reasoning || 'Data-driven recommendation'}
                      </Typography>
                    )}
                  </Paper>
                )) : (
                  <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                    Generating AI recommendations from comprehensive data...
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Detailed AI Analysis */}
          <Grid item xs={12}>
            <Card sx={{
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(25, 118, 210, 0.02) 100%)',
              border: 1,
              borderColor: 'primary.light'
            }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  color: 'primary.main',
                  fontWeight: 600
                }}>
                  🤖 Detailed AI Analysis
                </Typography>
                <Paper sx={{
                  p: 3,
                  backgroundColor: 'rgba(255,255,255,0.95)',
                  color: 'text.primary',
                  borderRadius: 2,
                  border: 1,
                  borderColor: 'divider'
                }}>
                  <Box
                    sx={{
                      '& h1, & h2, & h3, & h4, & h5, & h6': {
                        color: 'primary.main',
                        fontWeight: 600,
                        margin: 0,
                        marginBottom: 1.5,
                        '&:first-of-type': {
                          marginTop: 0
                        }
                      },
                      '& strong': {
                        fontWeight: 600,
                        color: 'primary.main'
                      },
                      '& ul, & ol': {
                        paddingLeft: 2,
                        margin: 0,
                        marginBottom: 1.5
                      },
                      '& li': {
                        marginBottom: 0.5,
                        lineHeight: 1.6
                      },
                      '& p': {
                        margin: 0,
                        marginBottom: 1.5,
                        lineHeight: 1.6,
                        '&:last-child': {
                          marginBottom: 0
                        }
                      },
                      '& em': {
                        fontStyle: 'italic',
                        color: 'text.secondary'
                      }
                    }}
                  >
                    <ReactMarkdown>
                      {insights}
                    </ReactMarkdown>
                  </Box>
                </Paper>
              </CardContent>
            </Card>
          </Grid>

          {/* Borough Statistics */}
          {marketDataStats?.boroughStats && marketDataStats.boroughStats.length > 0 && (
            <Grid item xs={12}>
              <Card sx={{
                background: 'linear-gradient(135deg, rgba(156, 39, 176, 0.05) 0%, rgba(156, 39, 176, 0.02) 100%)',
                border: 1,
                borderColor: 'secondary.light'
              }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    color: 'secondary.main',
                    fontWeight: 600,
                    mb: 3
                  }}>
                    📊 Borough Breakdown
                  </Typography>
                  <Grid container spacing={3}>
                    {marketDataStats.boroughStats.slice(0, 6).map((borough, index) => (
                      <Grid item xs={12} sm={6} md={4} key={index}>
                        <Paper sx={{
                          p: 2.5,
                          border: 1,
                          borderColor: 'divider',
                          borderRadius: 2,
                          backgroundColor: 'rgba(255,255,255,0.9)',
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: 2,
                            borderColor: 'secondary.main'
                          }
                        }}>
                          <Typography variant="subtitle1" fontWeight={600} color="secondary.main" sx={{ mb: 1.5 }}>
                            {borough._id || borough.borough}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            📍 {borough.count} neighborhoods
                          </Typography>
                          <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                            💰 Avg Rent: {formatCurrency(borough.avgRent)}
                          </Typography>
                          {borough.avgSafety && (
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              🛡️ Safety: {borough.avgSafety}/10
                            </Typography>
                          )}
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>

        <Paper sx={{
          mt: 4,
          p: 3,
          backgroundColor: 'rgba(25, 118, 210, 0.05)',
          borderRadius: 2,
          border: 1,
          borderColor: 'primary.light',
          textAlign: 'center'
        }}>
          <Typography variant="body2" color="text.secondary" sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 2,
            flexWrap: 'wrap'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              🕒 Last updated: {new Date(comprehensiveData?.overview?.lastUpdated || marketData?.data?.lastUpdated || Date.now()).toLocaleString()}
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              📊 Data points: {comprehensiveData?.overview?.totalDataPoints || dataPoints || 0} total
            </Box>
            {comprehensiveData?.overview?.dataSourcesUsed && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                🗄️ Sources: {comprehensiveData.overview.dataSourcesUsed.length} databases
              </Box>
            )}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              🤖 Powered by Gemini AI
            </Box>
          </Typography>
        </Paper>
      </Box>
    </>
  )
}

export default MarketInsights
