/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  background-color: #f5f5f5;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Utility classes */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Map container */
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Chat interface */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-input-container {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  background: white;
}

/* Message bubbles */
.message-bubble {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
}

.message-bubble.user {
  background: #1976d2;
  color: white;
  align-self: flex-end;
  margin-left: auto;
}

.message-bubble.assistant {
  background: #f5f5f5;
  color: #333;
  align-self: flex-start;
  border: 1px solid #e0e0e0;
}

/* Chart containers */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Responsive design */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 90%;
  }
  
  .chart-container {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .message-bubble {
    max-width: 95%;
  }
  
  .chart-container {
    height: 200px;
  }
}

/* Custom Material-UI overrides */
.MuiDrawer-paper {
  border-right: 1px solid #e0e0e0 !important;
}

.MuiAppBar-root {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* Neighborhood card styles */
.neighborhood-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.neighborhood-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

/* Search results */
.search-results {
  max-height: 400px;
  overflow-y: auto;
}

/* Similarity score indicator */
.similarity-score {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.similarity-score.high {
  background: #e8f5e8;
  color: #2e7d32;
}

.similarity-score.medium {
  background: #fff3e0;
  color: #f57c00;
}

.similarity-score.low {
  background: #ffebee;
  color: #d32f2f;
}
