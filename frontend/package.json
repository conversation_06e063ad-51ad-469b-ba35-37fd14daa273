{"name": "city-insights-ai-frontend", "version": "1.0.0", "description": "Frontend React app for City Insights AI", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@google/maps": "^1.1.3", "@mui/icons-material": "^5.15.0", "@mui/material": "^5.15.0", "axios": "^1.6.2", "chart.js": "^4.4.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lodash": "^4.17.21", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.4", "react-hot-toast": "^2.4.1", "react-markdown": "^10.1.0", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "recharts": "^2.15.3", "styled-components": "^6.1.6", "uuid": "^9.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/ui": "^1.0.4", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}, "engines": {"node": ">=18.0.0"}}