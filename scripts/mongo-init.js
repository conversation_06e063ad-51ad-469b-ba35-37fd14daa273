// MongoDB initialization script for local development
db = db.getSiblingDB('cityinsights');

// Create collections
db.createCollection('neighborhoods');
db.createCollection('crimedatas');

// Create indexes for neighborhoods collection
db.neighborhoods.createIndex({ "name": 1, "borough": 1 }, { unique: true });
db.neighborhoods.createIndex({ "coordinates": "2dsphere" });
db.neighborhoods.createIndex({ "housing.avgRent": 1 });
db.neighborhoods.createIndex({ "safety.safetyScore": -1 });
db.neighborhoods.createIndex({ "amenities.transitScore": -1 });
db.neighborhoods.createIndex({ "tags": 1 });
db.neighborhoods.createIndex({ "borough": 1 });

// Create indexes for crime data collection
db.crimedatas.createIndex({ "neighborhood": 1, "date": -1 });
db.crimedatas.createIndex({ "borough": 1, "incidentType": 1, "date": -1 });
db.crimedatas.createIndex({ "coordinates": "2dsphere" });
db.crimedatas.createIndex({ "year": 1, "month": 1 });
db.crimedatas.createIndex({ "category": 1, "severity": 1 });

print('Database initialized successfully');
