version: '3.8'

services:
  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: city-insights-backend
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - MONGODB_URI=${MONGODB_URI}
      - MONGODB_DB_NAME=${MONGODB_DB_NAME}
      - GOOGLE_CLOUD_PROJECT_ID=${GOOGLE_CLOUD_PROJECT_ID}
      - VERTEX_AI_LOCATION=${VERTEX_AI_LOCATION}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json
      - FRONTEND_URL=http://localhost:3000
    volumes:
      - ./credentials:/app/credentials:ro
      - backend-logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: city-insights-frontend
    ports:
      - "3000:80"
    environment:
      - VITE_API_URL=http://localhost:8080/api
      - VITE_GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB (for local development)
  mongodb:
    image: mongo:7.0
    container_name: city-insights-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-password}
      - MONGO_INITDB_DATABASE=${MONGODB_DB_NAME:-cityinsights}
    volumes:
      - mongodb-data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    restart: unless-stopped
    profiles:
      - local

  # Redis (for caching)
  redis:
    image: redis:7-alpine
    container_name: city-insights-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    profiles:
      - local

volumes:
  mongodb-data:
    driver: local
  redis-data:
    driver: local
  backend-logs:
    driver: local

networks:
  default:
    name: city-insights-network
