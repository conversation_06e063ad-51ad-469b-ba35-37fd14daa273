# Environment
NODE_ENV=development
PORT=8080

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173

# MongoDB
MONGODB_URI=mongodb+srv://username:<EMAIL>/cityinsights
MONGODB_DB_NAME=cityinsights

# Google Cloud Platform
GOOGLE_CLOUD_PROJECT_ID=your-project-id
VERTEX_AI_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# JWT Secret (for future auth implementation)
JWT_SECRET=your-super-secret-jwt-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Data Sources
NYC_OPEN_DATA_API_KEY=your-nyc-open-data-api-key
DATA_UPDATE_INTERVAL=24h
