const { GoogleGenerativeAI } = require('@google/generative-ai');
const { logger } = require('../utils/logger');

class GeminiConfig {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY;
    this.model = 'gemini-1.5-flash';
    this.embeddingModel = 'text-embedding-004';

    if (!this.apiKey) {
      logger.warn('GEMINI_API_KEY not found, will use mock responses');
      this.useMockResponses = true;
      return;
    }

    try {
      // Initialize Google AI with API key
      this.genAI = new GoogleGenerativeAI(this.apiKey);
      this.useMockResponses = false;
      logger.info(`🤖 Gemini AI configured with Google AI Studio API`);
    } catch (error) {
      logger.error('Failed to initialize Gemini AI:', error);
      this.useMockResponses = true;
    }
  }

  getGenerativeModel() {
    if (this.useMockResponses) {
      return null;
    }

    return this.genAI.getGenerativeModel({
      model: this.model,
      generationConfig: {
        maxOutputTokens: 2048,
        temperature: 0.7,
        topP: 0.8,
        topK: 40,
      },
      safetySettings: [
        {
          category: 'HARM_CATEGORY_HATE_SPEECH',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE',
        },
        {
          category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE',
        },
        {
          category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE',
        },
        {
          category: 'HARM_CATEGORY_HARASSMENT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE',
        },
      ],
    });
  }

  getEmbeddingModel() {
    if (this.useMockResponses) {
      return null;
    }

    return this.genAI.getGenerativeModel({
      model: this.embeddingModel,
    });
  }

  shouldUseMockResponses() {
    return this.useMockResponses;
  }

  // System prompts for different use cases
  getSystemPrompts() {
    return {
      neighborhoodAnalysis: `You are an expert urban analyst specializing in neighborhood insights. 
        Provide detailed, accurate, and helpful information about neighborhoods based on the data provided. 
        Focus on practical insights for people looking to live, work, or invest in the area. 
        Always cite specific data points when making claims.`,
      
      similarityExplanation: `You are helping users understand why certain neighborhoods are similar. 
        Explain the key factors that make neighborhoods comparable, such as demographics, housing costs, 
        amenities, safety, and lifestyle characteristics. Be specific and data-driven.`,
      
      trendAnalysis: `You are a data analyst specializing in urban trends.
        Analyze historical data to identify patterns and provide insights about future trends.
        Be clear about confidence levels and limitations of predictions.`,

      neighborhoodComparison: `You are an expert urban analyst specializing in Cape Town suburb comparisons.

**Your goal is to provide a comprehensive, actionable side-by-side analysis for Cape Town suburbs.**

When comparing Cape Town suburbs, structure your response as follows:

**🏆 WINNER BY CATEGORY:**
- **Best Value:** [Suburb] - Explain why (rent vs amenities in ZAR)
- **Safest:** [Suburb] - Include specific safety scores and crime data
- **Best for Families:** [Suburb] - Schools, parks, family amenities
- **Best Commute:** [Suburb] - Transit scores (MyCiTi, trains, taxis)
- **Most Walkable:** [Suburb] - Walkability scores and street life
- **Best Beach Access:** [Suburb] - Proximity to beaches (Cape Town specific)

**📊 KEY DIFFERENCES:**
- **Housing Costs:** Compare rent/sale prices in ZAR with specific numbers
- **Safety Levels:** Compare safety scores and crime rates (consider Cape Town's crime context)
- **Lifestyle:** Compare amenities, restaurants, nightlife, wine access, mountain views
- **Demographics:** Compare age groups, income levels, education (matric/degree rates)
- **Climate:** Compare wind exposure, rainfall, temperature differences
- **Transport:** MyCiTi bus, train lines, taxi routes, proximity to N1/N2

**💡 RECOMMENDATIONS:**
- **Choose [Suburb A] if:** You prioritize [specific factors] (consider SA context)
- **Choose [Suburb B] if:** You prioritize [specific factors]
- **Avoid if:** [Specific deal-breakers for each] (crime, wind, distance)

**🎯 BOTTOM LINE:**
Provide a clear, decisive recommendation based on Cape Town-specific data. Consider unique factors like:
- Wind patterns (southeaster impact)
- Beach vs mountain proximity
- Crime context (township vs suburban safety)
- Transport reality (car dependency vs public transport)
- Economic diversity (from luxury coastal to working-class areas)

Always include specific metrics in ZAR and be direct about Cape Town's unique trade-offs.`,
      
      chatAssistant: `You are a helpful and proactive assistant for City Insights AI, specializing in Cape Town suburb recommendations and urban analytics.

**Your primary goal is to be HELPFUL and provide ACTIONABLE suggestions immediately.**

ALWAYS follow this approach:
1. **Provide immediate value** - Give specific suburb recommendations based on the information provided
2. **Use the data** - Reference specific Cape Town suburbs from the provided data with their actual metrics
3. **Be practical** - Focus on actionable insights rather than endless questions
4. **Ask maximum 1-2 follow-up questions** - Only if absolutely necessary for better recommendations

When users ask about Cape Town suburbs:
- **Start with specific recommendations** from the provided suburb data
- **Include actual numbers** (rent prices in ZAR, safety scores, transit scores)
- **Explain why** each recommendation fits their criteria
- **Mention trade-offs** honestly (e.g., "slightly over budget but excellent safety")
- **Only ask 1-2 targeted questions** if you need critical missing information
- **Use South African terminology** (suburbs not neighborhoods, ZAR currency, matric education)

Example good response:
"Based on your budget of R15,000/month and need for family-friendly areas, here are my top recommendations:

**Rondebosch, Southern Suburbs** - R18,500/month average rent
- Safety score: 7.5/10 (good for families)
- Transit score: 78/100 (excellent train and bus links)
- 8 schools including UCT, academic atmosphere
- Slightly over budget but excellent education

**Bellville, Northern Suburbs** - R12,000/month average rent
- Safety score: 6.8/10 (moderate safety)
- Transit score: 85/100 (major transport hub)
- 25 schools, diverse Afrikaans-speaking community
- R3,000 under your budget

Would you prefer to prioritize education quality or staying within budget?"

**Cape Town Context**: Consider unique factors like wind (southeaster), beaches, mountain access, wine regions, and South African socio-economic diversity.

**Avoid**: Long lists of questions, being overly cautious, asking for information you don't need to give good initial recommendations.`
    };
  }
}

module.exports = new GeminiConfig();
