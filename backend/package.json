{"name": "city-insights-ai-backend", "version": "1.0.0", "description": "Backend API for City Insights AI - Urban analytics platform", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "data:import": "node scripts/dataImport.js", "data:ingest": "node scripts/dataIngestion.js quick", "data:ingest:full": "node scripts/dataIngestion.js full", "data:csv": "node scripts/csvImport.js sample", "data:csv:import": "node scripts/csvImport.js import", "setup:vector": "node scripts/setupVectorIndex.js", "setup:embeddings": "node scripts/dataIngestion.js embeddings"}, "keywords": ["urban-analytics", "ai", "mongodb", "vector-search", "gemini", "neighborhoods"], "author": "City Insights AI Team", "license": "MIT", "dependencies": {"@google-cloud/aiplatform": "^4.2.0", "@google-cloud/storage": "^7.7.0", "@google-cloud/vertexai": "^1.10.0", "@google/generative-ai": "^0.24.1", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/city-insights-ai.git"}, "bugs": {"url": "https://github.com/your-username/city-insights-ai/issues"}, "homepage": "https://github.com/your-username/city-insights-ai#readme"}