{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:24:18:2418"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:24:19:2419"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:24:19:2419"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:24:19:2419"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:24:19:2419"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:24:19:2419"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:24:28 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:24:28:2428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:24:28 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:24:28:2428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:24:38 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 84 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:24:38 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 171 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:24:38:2438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:24:48 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:24:48:2448"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:24:48 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 171 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:24:48:2448"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:24:50:2450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:25:01:251"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:25:01:251"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:25:01:251"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:25:01:251"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:25:01:251"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:25:01:251"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:25:14:2514"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:25:14:2514"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to MongoDB\u001b[39m","timestamp":"2025-06-12 15:25:14:2514"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mFound 0 rental properties to import\u001b[39m","timestamp":"2025-06-12 15:25:14:2514"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:25:31:2531"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:25:32:2532"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:25:32:2532"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:25:32:2532"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:25:32:2532"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:25:32:2532"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:26:21:2621"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:26:22:2622"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:26:22:2622"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:26:22:2622"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:26:22:2622"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:26:22:2622"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:26:22:2622"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:26:23:2623"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:26:23:2623"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:26:23:2623"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:26:23:2623"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:26:23:2623"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:26:24:2624"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:26:24:2624"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:26:24:2624"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:26:24:2624"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:26:24:2624"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:26:24:2624"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:26:26:2626"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:26:27:2627"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:26:27:2627"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:26:27:2627"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:26:27:2627"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:26:27:2627"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:26:27:2627"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:26:28:2628"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:26:28:2628"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:26:28:2628"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:26:28:2628"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:26:28:2628"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:26:31:2631"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:26:31:2631"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:26:31:2631"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:26:31:2631"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:26:31:2631"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:26:31:2631"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:26:37:2637"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:26:37:2637"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:26:37:2637"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:26:37:2637"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:26:37:2637"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:26:37:2637"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:26:42:2642"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:26:42:2642"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:26:42:2642"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:26:42:2642"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:26:42:2642"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:26:42:2642"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:26:46:2646"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:26:46:2646"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:26:46:2646"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:26:46:2646"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:26:46:2646"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:26:46:2646"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-12 15:26:47:2647"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:26:49:2649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:26:49:2649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to MongoDB\u001b[39m","timestamp":"2025-06-12 15:26:49:2649"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError importing house rentals: database is not defined\u001b[39m","stack":"ReferenceError: database is not defined\n    at importHouseRentals (/home/<USER>/aiinaction/backend/scripts/importHouseRentals.js:27:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-12 15:26:49:2649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to MongoDB\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase: test\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mFound 8 collections:\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m  - neighborhoods: 18 documents\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m  - taxi_routes: 1466 documents\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m  - houserentals: 0 documents\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m  - pub_hospitals: 41 documents\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m  - user_profiles: 8 documents\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m  - user_interactions: 14 documents\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m  - crimedatas: 6568 documents\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m  - pub_schools: 1479 documents\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mhouseRentals collection is empty or does not exist\u001b[39m","timestamp":"2025-06-12 15:27:03:273"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:28:59:2859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:30:32:3032"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:31:38:3138"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:32:48:3248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:32:48:3248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:33:54:3354"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:33:54:3354"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:33:54:3354"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:33:54:3354"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:33:54:3354"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:33:54:3354"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:12 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:12:3412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:12 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:12:3412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:27 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:27:3427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:27 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:27:3427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:40 +0000] \"GET /api/chat/suggestions HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:40:3440"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:45 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:45:3445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:45 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:45:3445"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:47 +0000] \"GET /api/search/filters HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:47:3447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:47 +0000] \"GET /api/neighborhoods?limit=50 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:47:3447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:47 +0000] \"GET /api/neighborhoods?limit=50 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:47:3447"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/hospitals/classifications HTTP/1.1\" 200 109 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/taxi-routes/origins HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/taxi-routes/destinations HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/hospitals/districts HTTP/1.1\" 200 161 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/taxi-routes/stats HTTP/1.1\" 200 680 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/hospitals/stats HTTP/1.1\" 200 550 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/schools/districts HTTP/1.1\" 200 138 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/schools/types HTTP/1.1\" 200 87 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/schools/mediums HTTP/1.1\" 200 132 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/hospitals/classifications HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/schools/stats HTTP/1.1\" 200 781 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:49 +0000] \"GET /api/hospitals?format=google-maps&limit=500 HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:49:3449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:50 +0000] \"GET /api/schools?format=google-maps&limit=500 HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:50:3450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:50 +0000] \"GET /api/taxi-routes/origins HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:50:3450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:50 +0000] \"GET /api/hospitals/districts HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:50:3450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:50 +0000] \"GET /api/taxi-routes/destinations HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:50:3450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:50 +0000] \"GET /api/taxi-routes?format=google-maps&limit=200 HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:50:3450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:50 +0000] \"GET /api/schools/districts HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:50:3450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:51 +0000] \"GET /api/taxi-routes/stats HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:51:3451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:51 +0000] \"GET /api/hospitals/stats HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:51:3451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:51 +0000] \"GET /api/schools/types HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:51:3451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:51 +0000] \"GET /api/schools/mediums HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:51:3451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:51 +0000] \"GET /api/hospitals?format=google-maps&limit=500 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:51:3451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:51 +0000] \"GET /api/schools/stats HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:51:3451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:51 +0000] \"GET /api/schools?format=google-maps&limit=500 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:51:3451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:51 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:51:3451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:51 +0000] \"GET /api/taxi-routes?format=google-maps&limit=200 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:51:3451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:34:51 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:34:51:3451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:05 +0000] \"GET /api/search/filters HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:05:355"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:05 +0000] \"GET /api/neighborhoods?limit=50 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:05:355"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:05 +0000] \"GET /api/neighborhoods?limit=50 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:05:355"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:06 +0000] \"GET /api/taxi-routes/origins HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:06:356"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:06 +0000] \"GET /api/taxi-routes/destinations HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:06:356"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:06 +0000] \"GET /api/hospitals/classifications HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:06:356"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:07 +0000] \"GET /api/hospitals/districts HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:07:357"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:07 +0000] \"GET /api/taxi-routes/stats HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:07:357"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:07 +0000] \"GET /api/schools/types HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:07:357"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:07 +0000] \"GET /api/schools/districts HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:07:357"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:07 +0000] \"GET /api/schools/mediums HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:07:357"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:08 +0000] \"GET /api/taxi-routes?format=google-maps&limit=200 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:08:358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:08 +0000] \"GET /api/schools/stats HTTP/1.1\" 200 781 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:08:358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:08 +0000] \"GET /api/hospitals?format=google-maps&limit=500 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:08:358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:08 +0000] \"GET /api/taxi-routes/origins HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:08:358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:08 +0000] \"GET /api/schools?format=google-maps&limit=500 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:08:358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:08 +0000] \"GET /api/hospitals/stats HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:08:358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:08 +0000] \"GET /api/taxi-routes/destinations HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:08:358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:09 +0000] \"GET /api/hospitals/districts HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:09:359"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:09 +0000] \"GET /api/hospitals/classifications HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:09:359"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:09 +0000] \"GET /api/schools/types HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:09:359"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:09 +0000] \"GET /api/schools/districts HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:09:359"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:09 +0000] \"GET /api/taxi-routes/stats HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:09:359"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:09 +0000] \"GET /api/schools/mediums HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:09:359"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:11 +0000] \"GET /api/taxi-routes?format=google-maps&limit=200 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:11:3511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:11 +0000] \"GET /api/schools/stats HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:11:3511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:11 +0000] \"GET /api/hospitals/stats HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:11:3511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:11 +0000] \"GET /api/hospitals?format=google-maps&limit=500 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:11:3511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:12 +0000] \"GET /api/schools?format=google-maps&limit=500 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:12:3512"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:12 +0000] \"GET /api/chat/suggestions HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:12:3512"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:20 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:20:3520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:26 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:26:3526"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:35:26 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:35:26:3526"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [12/Jun/2025:13:37:16 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:37:16:3716"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [12/Jun/2025:13:37:16 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:37:16:3716"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [12/Jun/2025:13:37:17 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:37:17:3717"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [12/Jun/2025:13:37:17 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:37:17:3717"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [12/Jun/2025:13:37:17 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:37:17:3717"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [12/Jun/2025:13:37:17 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:37:17:3717"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-12 15:39:59:3959"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:40:10:4010"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:40:10:4010"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:40:10:4010"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:40:10:4010"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:40:10:4010"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:40:10:4010"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:40:12 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:40:12:4012"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:40:12 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:40:12:4012"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:40:24 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:40:24:4024"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:40:24 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:40:24:4024"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:40:27 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:40:27:4027"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:40:28 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:40:28:4028"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:13:41:57 +0000] \"GET /api/house-rentals?maxPrice=100000&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 15:41:57:4157"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:43:39:4339"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:43:40:4340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGTERM received, shutting down gracefully\u001b[39m","timestamp":"2025-06-12 15:44:12:4412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 15:44:23:4423"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 15:44:23:4423"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 15:44:23:4423"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 15:44:23:4423"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 15:44:23:4423"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 15:44:23:4423"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGTERM received, shutting down gracefully\u001b[39m","timestamp":"2025-06-12 15:45:04:454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:46:20:4620"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:46:23:4623"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 16:46:23:4623"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 16:46:23:4623"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 16:46:23:4623"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 16:46:23:4623"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:46:44 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:46:44:4644"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:46:44 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:46:44:4644"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:47:07 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:47:07:477"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:47:08 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:47:08:478"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-12 16:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:48:05:485"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:48:07:487"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 16:48:07:487"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 16:48:07:487"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 16:48:07:487"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 16:48:07:487"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:48:33 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:48:33:4833"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:48:34 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:48:34:4834"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:48:45 +0000] \"GET /api/house-rentals?maxPrice=100000&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:48:45:4845"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:48:50 +0000] \"GET /api/house-rentals?location=cape+town&maxPrice=100000&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:48:50:4850"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:48:52:4852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:48:54:4854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 16:48:54:4854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 16:48:54:4854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 16:48:54:4854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 16:48:54:4854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:49:37 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:49:37:4937"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:49:37 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:49:37:4937"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:49:59:4959"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:50:00:500"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 16:50:00:500"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 16:50:00:500"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 16:50:00:500"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 16:50:00:500"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:50:52:5052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:53:06:536"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:53:06:536"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:53:08:538"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 16:53:08:538"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8081\u001b[39m","timestamp":"2025-06-12 16:53:08:538"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 16:53:08:538"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8081/health\u001b[39m","timestamp":"2025-06-12 16:53:08:538"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:53:09:539"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-12 16:53:21:5321"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:53:25:5325"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:53:26:5326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 16:53:26:5326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8081\u001b[39m","timestamp":"2025-06-12 16:53:26:5326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 16:53:26:5326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8081/health\u001b[39m","timestamp":"2025-06-12 16:53:26:5326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:53:31:5331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:53:33:5333"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:54:26:5426"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:54:29:5429"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 16:54:29:5429"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8082\u001b[39m","timestamp":"2025-06-12 16:54:29:5429"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 16:54:29:5429"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8082/health\u001b[39m","timestamp":"2025-06-12 16:54:29:5429"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:55:11:5511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:55:13:5513"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 16:55:13:5513"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8082\u001b[39m","timestamp":"2025-06-12 16:55:13:5513"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 16:55:13:5513"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8082/health\u001b[39m","timestamp":"2025-06-12 16:55:13:5513"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-12 16:56:04:564"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:56:15:5615"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:56:17:5617"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 16:56:17:5617"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 16:56:17:5617"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 16:56:17:5617"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 16:56:17:5617"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:56:25 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:56:25:5625"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:56:26 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:56:26:5626"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:56:30 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:56:30:5630"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:56:30 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:56:30:5630"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:56:34 +0000] \"GET /api/house-rentals?maxPrice=100000&q=cape+town&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:56:34:5634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:56:39 +0000] \"GET /api/house-rentals?maxPrice=100000&category=Budget&q=cape+town&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:56:39:5639"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:58:29 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:58:29:5829"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:58:31 +0000] \"GET /api/house-rentals?maxPrice=100000&category=Moderate&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:58:31:5831"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:58:34 +0000] \"GET /api/house-rentals?maxPrice=100000&bedrooms=1&category=Moderate&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:58:34:5834"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:58:36 +0000] \"GET /api/house-rentals?maxPrice=100000&bedrooms=1&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:58:36:5836"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 16:58:51:5851"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 16:58:53:5853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 16:58:53:5853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 16:58:53:5853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 16:58:53:5853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 16:58:53:5853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:58:57 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" - - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:58:57:5857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:58:58 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:58:58:5858"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:59:32 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:59:32:5932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:59:33 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:59:33:5933"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:59:39 +0000] \"GET /api/house-rentals?maxPrice=100000&q=cape+town&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:59:39:5939"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:14:59:42 +0000] \"GET /api/house-rentals?maxPrice=100000&category=Budget&q=cape+town&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 16:59:42:5942"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [12/Jun/2025:15:00:28 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 77471 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-12 17:00:28:028"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:09:12 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:09:12:912"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-12 17:09:12:912"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:09:12 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:09:12:912"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB reconnected\u001b[39m","timestamp":"2025-06-12 17:09:19:919"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:09:19 +0000] \"GET /api/house-rentals?maxPrice=100000&bedrooms=1&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:09:19:919"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:09:22 +0000] \"GET /api/house-rentals?maxPrice=100000&bedrooms=1&category=Budget&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:09:22:922"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:09:26 +0000] \"GET /api/house-rentals?maxPrice=100000&bedrooms=1&category=Budget&q=wynberg&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:09:26:926"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:09:32 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:09:32:932"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:09:33 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:09:33:933"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 17:09:48:948"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 17:09:49:949"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-12 17:12:29:1229"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 17:12:34:1234"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 17:12:35:1235"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 17:12:35:1235"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 17:12:35:1235"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 17:12:35:1235"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 17:12:35:1235"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-12 17:13:37:1337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 17:13:40:1340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 17:13:41:1341"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 17:13:41:1341"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 17:13:41:1341"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 17:13:41:1341"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 17:13:41:1341"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:13:46 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:13:46:1346"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:13:47 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:13:47:1347"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:13:50 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:13:50:1350"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:15:13:51 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 17:13:51:1351"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-12 20:39:07:397"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-12 20:39:09:399"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-12 20:39:09:399"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-12 20:39:09:399"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-12 20:39:09:399"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-12 20:39:09:399"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:18:39:39 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 20:39:39:3939"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:18:39:39 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 20:39:39:3939"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:18:39:42 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 20:39:42:3942"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:18:39:42 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 20:39:42:3942"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [12/Jun/2025:18:39:56 +0000] \"GET /api/house-rentals?location=cape+town&maxPrice=100000&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-12 20:39:56:3956"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 08:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 08:47:11:4711"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 08:47:11:4711"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 08:47:11:4711"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 08:47:11:4711"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 08:47:11:4711"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:06:47:20 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 08:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:06:47:20 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 08:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:06:47:27 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 08:47:27:4727"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:06:47:27 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 08:47:27:4727"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-13 08:48:26:4826"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 09:46:18:4618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 09:46:19:4619"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 09:46:19:4619"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 09:46:19:4619"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 09:46:19:4619"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 09:46:19:4619"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:07:46:34 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:46:34:4634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:07:46:34 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:46:34:4634"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:07:46:38 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:46:38:4638"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:07:46:38 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:46:38:4638"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:07:47:01 +0000] \"GET /api/house-rentals?minPrice=0&maxPrice=100000&q=he&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:47:01:471"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:47:40 +0000] \"GET /api/house-rentals?limit=5 HTTP/1.1\" 200 4352 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 09:47:40:4740"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 09:47:46:4746"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 09:47:47:4747"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:47:57 +0000] \"GET /api/house-rentals?limit=5 HTTP/1.1\" 200 4352 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 09:47:57:4757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:07:48:45 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:48:45:4845"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [13/Jun/2025:07:48:46 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:48:46:4846"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-13 09:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB reconnected\u001b[39m","timestamp":"2025-06-13 09:49:05:495"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:49:29 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 10288 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 09:49:29:4929"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:50:01 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5174/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:50:01:501"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:50:01 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:50:01:501"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:50:03 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:50:03:503"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:50:03 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:50:03:503"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-13 09:50:21:5021"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 09:50:45:5045"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 09:50:47:5047"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 09:50:47:5047"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 09:50:47:5047"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 09:50:47:5047"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 09:50:47:5047"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:50:49 +0000] \"GET /api/house-rentals?limit=5 HTTP/1.1\" 200 4352 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 09:50:49:5049"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:51:04 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:51:04:514"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:51:05 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:51:05:515"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:51:05 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:51:05:515"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:51:05 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:51:05:515"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:51:10 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:51:10:5110"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:51:10 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:51:10:5110"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:52:35 +0000] \"GET /api/house-rentals?limit=5&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5174/rental-test\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:52:35:5235"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:52:35 +0000] \"GET /api/house-rentals?limit=5&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/rental-test\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:52:35:5235"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:52:50 +0000] \"GET /api/house-rentals?limit=1 HTTP/1.1\" 200 954 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 09:52:50:5250"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:53:10 +0000] \"GET /api/house-rentals?limit=5&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/rental-test\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:53:10:5310"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:53:23 +0000] \"GET /api/house-rentals?limit=5&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/rental-test\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:53:23:5323"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:53:38 +0000] \"GET /api/house-rentals?limit=5&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/rental-test\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:53:38:5338"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:53:38 +0000] \"GET /api/house-rentals?limit=5&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/rental-test\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:53:38:5338"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:53:44 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5174/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:53:44:5344"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:53:45 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5174/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:53:45:5345"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:55:10 +0000] \"GET /api/house-rentals?q=apartment&limit=3 HTTP/1.1\" 200 2639 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 09:55:10:5510"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:55:18 +0000] \"GET /api/house-rentals?q=apartment&limit=3 HTTP/1.1\" 200 2639 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 09:55:18:5518"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-13 09:56:56:5656"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 09:57:08:578"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 09:57:09:579"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 09:57:09:579"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 09:57:09:579"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 09:57:09:579"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 09:57:09:579"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:59:17 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:59:17:5917"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:59:17 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 200 - \"http://localhost:5173/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:59:17:5917"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:59:20 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:59:20:5920"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:59:22 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:59:22:5922"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-13 09:59:35:5935"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB reconnected\u001b[39m","timestamp":"2025-06-13 09:59:46:5946"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:59:48 +0000] \"GET /api/house-rentals?minPrice=0&maxPrice=100000&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:59:48:5948"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:07:59:58 +0000] \"GET /api/house-rentals?minPrice=0&maxPrice=100000&q=cape+town&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 09:59:58:5958"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:00:11 +0000] \"GET /api/house-rentals?minPrice=0&maxPrice=100000&q=cape+town&limit=12&offset=12&sortBy=price&sortOrder=asc HTTP/1.1\" 200 993 \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:00:11:011"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:00:22 +0000] \"GET /api/house-rentals?minPrice=0&maxPrice=100000&category=Moderate&limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 200 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:00:22:022"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:00:39 +0000] \"GET /api/chat/suggestions HTTP/1.1\" 200 932 \"http://localhost:5173/chat\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:00:39:039"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:00:55 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 - \"http://localhost:5173/insights\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:00:55:055"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:02:59:259"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:03:00:30"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:03:00:30"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:03:00:30"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:03:00:30"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:03:00:30"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:03:19:319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:03:21:321"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:03:21:321"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:03:21:321"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:03:21:321"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:03:21:321"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:04:04:44"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:04:05:45"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:04:05:45"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:04:05:45"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:04:05:45"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:04:05:45"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:04:24:424"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:04:25:425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:04:25:425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:04:25:425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:04:25:425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:04:25:425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:04:55:455"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:04:56:456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:04:56:456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:04:56:456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:04:56:456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:04:56:456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:05:30:530"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:05:32:532"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:05:32:532"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:05:32:532"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:05:32:532"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:05:32:532"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:09:33 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 277193 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 10:09:33:933"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:10:42:1042"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:10:44:1044"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-13 10:10:48:1048"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:10:52:1052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:10:53:1053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:10:55:1055"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-02.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:10:57:1057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:10:57:1057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:10:57:1057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:10:57:1057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:10:57:1057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:12:59 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 - \"http://localhost:5173/insights\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:12:59:1259"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:25 +0000] \"GET /api/house-rentals?limit=12&offset=0&sortBy=price&sortOrder=asc HTTP/1.1\" 304 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:25:1325"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:26 +0000] \"GET /api/house-rentals/stats HTTP/1.1\" 200 - \"http://localhost:5173/rentals\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:26:1326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:30 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:30:1330"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:31 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 304 - \"http://localhost:5173/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:31:1331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:31 +0000] \"GET /api/search/filters HTTP/1.1\" 200 257 \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:31:1331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:31 +0000] \"GET /api/neighborhoods?limit=50 HTTP/1.1\" 200 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:31:1331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:31 +0000] \"GET /api/neighborhoods?limit=50 HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:31:1331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:34 +0000] \"GET /api/taxi-routes/origins HTTP/1.1\" 200 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:34:1334"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:34 +0000] \"GET /api/hospitals/classifications HTTP/1.1\" 200 109 \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:34:1334"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:34 +0000] \"GET /api/taxi-routes/destinations HTTP/1.1\" 200 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:34:1334"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:34 +0000] \"GET /api/hospitals/districts HTTP/1.1\" 200 161 \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:34:1334"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:36 +0000] \"GET /api/taxi-routes/stats HTTP/1.1\" 200 680 \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:36:1336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:36 +0000] \"GET /api/hospitals/stats HTTP/1.1\" 200 550 \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:36:1336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:36 +0000] \"GET /api/schools/types HTTP/1.1\" 200 87 \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:36:1336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:36 +0000] \"GET /api/schools/districts HTTP/1.1\" 200 138 \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:36:1336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:36 +0000] \"GET /api/schools/mediums HTTP/1.1\" 200 132 \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:36:1336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:37 +0000] \"GET /api/schools/stats HTTP/1.1\" 200 781 \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:37:1337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:37 +0000] \"GET /api/hospitals?format=google-maps&limit=500 HTTP/1.1\" 200 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:37:1337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:38 +0000] \"GET /api/hospitals/classifications HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:38:1338"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:38 +0000] \"GET /api/taxi-routes/origins HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:38:1338"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:38 +0000] \"GET /api/schools?format=google-maps&limit=500 HTTP/1.1\" 200 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:38:1338"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:39 +0000] \"GET /api/taxi-routes/destinations HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:39:1339"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:39 +0000] \"GET /api/taxi-routes?format=google-maps&limit=200 HTTP/1.1\" 200 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:39:1339"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:39 +0000] \"GET /api/hospitals/districts HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:39:1339"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:40 +0000] \"GET /api/schools/types HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:40:1340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:40 +0000] \"GET /api/taxi-routes/stats HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:40:1340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:40 +0000] \"GET /api/hospitals/stats HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:40:1340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:41 +0000] \"GET /api/schools/districts HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:41:1341"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:41 +0000] \"GET /api/schools/mediums HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:41:1341"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:42 +0000] \"GET /api/schools/stats HTTP/1.1\" 200 781 \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:42:1342"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:42 +0000] \"GET /api/hospitals?format=google-maps&limit=500 HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:42:1342"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:43 +0000] \"GET /api/schools?format=google-maps&limit=500 HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:43:1343"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:13:44 +0000] \"GET /api/taxi-routes?format=google-maps&limit=200 HTTP/1.1\" 304 - \"http://localhost:5173/explorer\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:13:44:1344"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB reconnected\u001b[39m","timestamp":"2025-06-13 10:16:08:168"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:19:39 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 - \"http://localhost:5173/insights\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:19:39:1939"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-06-13 10:25:33:2533"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:38:03:383"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:38:03:383"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:38:03:383"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:38:03:383"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:38:03:383"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:38:03:383"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:38:23 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 - \"http://localhost:5173/insights\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:38:23:3823"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:39:18 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"http://localhost:5173/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:39:18:3918"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:39:18 +0000] \"GET /api/neighborhoods?sortBy=safetyScore&sortOrder=desc&limit=20 HTTP/1.1\" 304 - \"http://localhost:5173/dashboard\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:39:18:3918"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:39:35 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 - \"http://localhost:5173/insights\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:39:35:3935"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:40:07 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 - \"http://localhost:5173/insights\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:40:07:407"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:41:30 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 277408 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 10:41:30:4130"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:43:12:4312"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:43:12:4312"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:43:40 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 278002 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 10:43:40:4340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:46:10 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 - \"http://localhost:5173/insights\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:46:10:4610"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:47:43 +0000] \"GET /api/neighborhoods HTTP/1.1\" 200 33377 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 10:47:43:4743"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:48:32 +0000] \"GET /api/neighborhoods HTTP/1.1\" 200 33377 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 10:48:32:4832"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:49:12 +0000] \"GET /api/neighborhoods HTTP/1.1\" 200 33377 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 10:49:12:4912"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:50:56 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 10:50:56:5056"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:51:06:516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:51:07:517"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:51:07:517"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:51:07:517"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:51:07:517"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:51:07:517"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:51:21:5121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:51:21:5121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:51:21:5121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:51:21:5121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:51:21:5121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:51:21:5121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:51:33:5133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:51:33:5133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:51:33:5133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:51:33:5133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:51:33:5133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:51:33:5133"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:51:49:5149"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:51:49:5149"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:51:49:5149"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:51:49:5149"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:51:49:5149"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:51:49:5149"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:52:19:5219"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:52:20:5220"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:52:20:5220"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:52:20:5220"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:52:20:5220"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:52:20:5220"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:52:32:5232"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:52:33:5233"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:52:33:5233"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:52:33:5233"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:52:33:5233"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:52:33:5233"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:52:47:5247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-01.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:52:48:5248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:52:48:5248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:52:48:5248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:52:48:5248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:52:48:5248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:53:19 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 - \"http://localhost:5173/insights\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:53:19:5319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🤖 Gemini AI configured with Google AI Studio API\u001b[39m","timestamp":"2025-06-13 10:55:06:556"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🍃 MongoDB Connected: ac-wuui4fo-shard-00-00.6fdreqp.mongodb.net\u001b[39m","timestamp":"2025-06-13 10:55:06:556"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Database: test\u001b[39m","timestamp":"2025-06-13 10:55:06:556"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 City Insights AI Backend running on port 8080\u001b[39m","timestamp":"2025-06-13 10:55:06:556"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-13 10:55:06:556"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:8080/health\u001b[39m","timestamp":"2025-06-13 10:55:06:556"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:55:15 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 - \"http://localhost:5173/insights\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-13 10:55:15:5515"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:56:18 +0000] \"GET /api/neighborhoods/market-insights HTTP/1.1\" 200 284286 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 10:56:18:5618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [13/Jun/2025:08:56:52 +0000] \"GET /api/analytics/stats HTTP/1.1\" 200 369 \"-\" \"curl/7.81.0\"\u001b[39m","timestamp":"2025-06-13 10:56:52:5652"}
