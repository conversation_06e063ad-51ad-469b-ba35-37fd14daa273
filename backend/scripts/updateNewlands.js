require('dotenv').config();
const mongoose = require('mongoose');
const Neighborhood = require('../src/models/Neighborhood');
const { logger } = require('../src/utils/logger');

async function updateNewlands() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    logger.info('Connected to MongoDB');

    // Find Newlands neighborhood
    const newlands = await Neighborhood.findOne({ name: "Newlands" });
    
    if (newlands) {
      logger.info('Found Newlands, updating coordinates...');
      logger.info('Old coordinates:', newlands.coordinates);
      
      // Update the coordinates
      newlands.coordinates = { lat: -33.975507, lng: 18.464504 };
      await newlands.save();
      
      logger.info(`Updated Newlands coordinates to: ${newlands.coordinates.lat}, ${newlands.coordinates.lng}`);
      
      // Verify the update
      const updatedNewlands = await Neighborhood.findOne({ name: "Newlands" });
      logger.info('Verified coordinates:', updatedNewlands.coordinates);
      
    } else {
      logger.error('Newlands neighborhood not found in database');
    }

    logger.info('Newlands coordinates successfully updated!');
    
  } catch (error) {
    logger.error('Error updating Newlands:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

// Run the script
updateNewlands();
