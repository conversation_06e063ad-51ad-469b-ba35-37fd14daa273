const { MongoClient } = require('mongodb');
require('dotenv').config();

async function testHouseRentalsDB() {
  let client;
  
  try {
    console.log('Connecting to MongoDB...');
    client = new MongoClient(process.env.MONGODB_URI);
    await client.connect();
    console.log('Connected successfully');
    
    // List all databases
    const adminDb = client.db().admin();
    const databases = await adminDb.listDatabases();
    console.log('\nAvailable databases:');
    databases.databases.forEach(db => {
      console.log(`  - ${db.name} (${db.sizeOnDisk} bytes)`);
    });
    
    // Try to access houseRentals database
    console.log('\nTrying to access houseRentals database...');
    const houseRentalsDB = client.db('houseRentals');
    
    // List collections in houseRentals database
    const collections = await houseRentalsDB.listCollections().toArray();
    console.log(`Collections in houseRentals database: ${collections.length}`);
    collections.forEach(col => {
      console.log(`  - ${col.name}`);
    });
    
    // Check properties collection
    if (collections.some(col => col.name === 'properties')) {
      const propertiesCollection = houseRentalsDB.collection('properties');
      const count = await propertiesCollection.countDocuments();
      console.log(`\nProperties collection has ${count} documents`);
      
      if (count > 0) {
        const sample = await propertiesCollection.findOne();
        console.log('\nSample property:');
        console.log(JSON.stringify(sample, null, 2));
      }
    } else {
      console.log('\nNo properties collection found in houseRentals database');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    if (client) {
      await client.close();
    }
  }
}

testHouseRentalsDB();
