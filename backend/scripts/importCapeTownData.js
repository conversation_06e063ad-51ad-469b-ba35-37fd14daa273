require('dotenv').config();
const mongoose = require('mongoose');
const Neighborhood = require('../src/models/Neighborhood');
const CrimeData = require('../src/models/CrimeData');
const geminiService = require('../src/services/geminiService');
const { logger } = require('../src/utils/logger');

// Enhanced Cape Town Rental Market Data 2025 - Comprehensive Analysis
// Based on Property24, Private Property, Seeff, PayProp market reports
const capeReggSuburbs = [
  // ATLANTIC SEABOARD - Premium Coastal Areas
  {
    name: "Camps Bay",
    borough: "Atlantic Seaboard",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.9553, lng: 18.3756 },
    demographics: {
      population: 4200,
      medianAge: 42,
      medianIncome: 750000, // ZAR annually
      ethnicComposition: {
        white: 72, black: 8, coloured: 15, indian: 3, other: 2
      },
      educationLevel: {
        matric: 96, diploma: 48, degree: 68
      }
    },
    housing: {
      avgRent: 67500, // Weighted average across all bedroom types
      rentalRange: { min: 25000, max: 180000 },
      rentByBedroom: {
        studio: { 
          min: 25000, max: 40000, avg: 32500,
          typical: "Modern studio with sea glimpses, secure complex",
          features: ["Sea views", "Pool access", "Security", "Parking"]
        },
        oneBed: { 
          min: 30000, max: 55000, avg: 42500,
          typical: "Ocean view apartment with balcony, luxury finishes",
          features: ["Ocean views", "Balcony", "Pool", "Gym", "Security"]
        },
        twoBed: { 
          min: 45000, max: 85000, avg: 65000,
          typical: "Sea-facing apartment with 2 balconies, premium location",
          features: ["Direct sea views", "Multiple balconies", "Premium appliances", "Concierge"]
        },
        threeBed: { 
          min: 70000, max: 140000, avg: 105000,
          typical: "Luxury penthouse or house with panoramic views",
          features: ["Panoramic views", "Private garden/terrace", "Premium finishes", "Guest parking"]
        },
        fourBed: { 
          min: 100000, max: 180000, avg: 140000,
          typical: "Exclusive villa or luxury house, beachfront location",
          features: ["Beachfront", "Private pool", "Multiple terraces", "Staff quarters"]
        }
      },
      avgSalePrice: 15000000,
      rentalAvailability: 8, // Limited luxury stock
      homeOwnershipRate: 82,
      pricePerSqM: 65000,
      rentalYield: 4.8,
      propertyTypes: {
        apartments: 75, houses: 20, penthouses: 5
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet", "DSTV"],
        avgUtilityCost: 2500
      }
    },
    safety: {
      crimeRate: 1.8,
      safetyScore: 8.7,
      crimeTypes: { violent: 2, property: 15, drug: 3, other: 4 },
      securityFeatures: ["24/7 Security", "Access control", "CCTV", "Patrols"]
    },
    amenities: {
      restaurants: 45,
      schools: 2,
      parks: 3,
      transitScore: 55,
      walkabilityScore: 82,
      bikeScore: 65,
      groceryStores: 8,
      hospitals: 0,
      beaches: 2,
      entertainment: ["Camps Bay Strip", "Beach clubs", "Sunset bars"],
      shopping: ["The Promenade", "Local boutiques"]
    },
    description: "Exclusive beachfront suburb with world-famous beaches and Twelve Apostles mountain backdrop. Premium rental market with luxury apartments and houses.",
    tags: ["luxury", "beachfront", "scenic", "expensive", "tourist destination", "celebrities", "premium rental"],
    climate: {
      avgTemp: 18,
      rainyDays: 65,
      windyDays: 125,
      bestMonths: ["Nov", "Dec", "Jan", "Feb", "Mar"]
    },
    transport: {
      uberAvg: 45, // Minutes to city center
      publicTransport: ["MyCiTi bus"],
      parking: "Street parking limited, secure parking premium"
    }
  },

  {
    name: "Sea Point",
    borough: "Atlantic Seaboard",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.9239, lng: 18.3896 },
    demographics: {
      population: 11500,
      medianAge: 38,
      medianIncome: 420000,
      ethnicComposition: {
        white: 45, black: 12, coloured: 35, indian: 6, other: 2
      },
      educationLevel: {
        matric: 89, diploma: 42, degree: 51
      }
    },
    housing: {
      avgRent: 35000,
      rentalRange: { min: 15000, max: 75000 },
      rentByBedroom: {
        studio: { 
          min: 15000, max: 22000, avg: 18500,
          typical: "Compact modern studio, ocean glimpses, building amenities",
          features: ["Ocean glimpses", "Pool", "Gym", "Security"]
        },
        oneBed: { 
          min: 18000, max: 35000, avg: 26500,
          typical: "Sea view apartment with balcony, modern kitchen",
          features: ["Sea views", "Balcony", "Modern appliances", "Building pool"]
        },
        twoBed: { 
          min: 28000, max: 50000, avg: 39000,
          typical: "Spacious apartment with sea views, 2 bathrooms",
          features: ["Sea views", "2 bathrooms", "Open plan", "Security parking"]
        },
        threeBed: { 
          min: 40000, max: 70000, avg: 55000,
          typical: "Large family apartment or house, multiple sea-facing rooms",
          features: ["Multiple sea views", "Family-friendly", "Storage", "Balconies"]
        },
        fourBed: { 
          min: 55000, max: 75000, avg: 65000,
          typical: "Luxury apartment or house, rare in Sea Point",
          features: ["Premium location", "Multiple bathrooms", "Staff room", "Storage"]
        }
      },
      avgSalePrice: 4200000,
      rentalAvailability: 15, // Good apartment stock
      homeOwnershipRate: 65,
      pricePerSqM: 42000,
      rentalYield: 6.2,
      propertyTypes: {
        apartments: 90, houses: 8, penthouses: 2
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 2000
      }
    },
    safety: {
      crimeRate: 2.3,
      safetyScore: 7.8,
      crimeTypes: { violent: 4, property: 18, drug: 5, other: 6 },
      securityFeatures: ["Building security", "CCTV", "Access control"]
    },
    amenities: {
      restaurants: 65,
      schools: 4,
      parks: 3,
      transitScore: 75,
      walkabilityScore: 91,
      bikeScore: 78,
      groceryStores: 15,
      hospitals: 1,
      beaches: 1,
      entertainment: ["Sea Point Promenade", "Pavilion", "Comedy clubs"],
      shopping: ["Sea Point Plaza", "Local shops"]
    },
    description: "Vibrant apartment-living suburb with promenade, diverse community, and excellent dining. Popular for 1-2 bedroom apartments.",
    tags: ["apartments", "diverse", "walkable", "dining", "promenade", "urban beach living"],
    climate: {
      avgTemp: 18,
      rainyDays: 68,
      windyDays: 118,
      bestMonths: ["Nov", "Dec", "Jan", "Feb"]
    },
    transport: {
      uberAvg: 25,
      publicTransport: ["MyCiTi bus", "Taxi routes"],
      parking: "Street parking challenging, building parking available"
    }
  },

  {
    name: "Green Point",
    borough: "Atlantic Seaboard",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.9067, lng: 18.4067 },
    demographics: {
      population: 8900,
      medianAge: 35,
      medianIncome: 485000,
      ethnicComposition: {
        white: 52, black: 15, coloured: 25, indian: 6, other: 2
      },
      educationLevel: {
        matric: 91, diploma: 45, degree: 58
      }
    },
    housing: {
      avgRent: 42000,
      rentalRange: { min: 20000, max: 95000 },
      rentByBedroom: {
        studio: { 
          min: 20000, max: 28000, avg: 24000,
          typical: "Modern studio in new development, stadium proximity",
          features: ["Modern finishes", "Gym", "Pool", "Security"]
        },
        oneBed: { 
          min: 25000, max: 42000, avg: 33500,
          typical: "Contemporary apartment with city/sea views",
          features: ["City/sea views", "Balcony", "Modern kitchen", "Pool"]
        },
        twoBed: { 
          min: 35000, max: 65000, avg: 50000,
          typical: "Spacious apartment in luxury complex near stadium",
          features: ["Stadium proximity", "Premium finishes", "2 bathrooms", "Gym"]
        },
        threeBed: { 
          min: 50000, max: 85000, avg: 67500,
          typical: "Large apartment or townhouse, family-oriented",
          features: ["Family space", "Multiple bathrooms", "Storage", "Parking"]
        },
        fourBed: { 
          min: 70000, max: 95000, avg: 82500,
          typical: "Luxury house or penthouse, rare availability",
          features: ["Premium location", "Private garden", "Multiple parking", "Views"]
        }
      },
      avgSalePrice: 5800000,
      rentalAvailability: 12,
      homeOwnershipRate: 58,
      pricePerSqM: 48000,
      rentalYield: 5.8,
      propertyTypes: {
        apartments: 85, townhouses: 10, houses: 5
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 2200
      }
    },
    safety: {
      crimeRate: 2.1,
      safetyScore: 8.2,
      crimeTypes: { violent: 3, property: 16, drug: 4, other: 5 },
      securityFeatures: ["24/7 Security", "CCTV", "Access control", "Patrols"]
    },
    amenities: {
      restaurants: 55,
      schools: 3,
      parks: 4,
      transitScore: 78,
      walkabilityScore: 88,
      bikeScore: 82,
      groceryStores: 12,
      hospitals: 0,
      beaches: 1,
      entertainment: ["DHL Stadium", "Green Point Park", "V&A Waterfront nearby"],
      shopping: ["Local boutiques", "V&A Waterfront"]
    },
    description: "Modern residential area near DHL Stadium and V&A Waterfront, popular with young professionals and sports enthusiasts.",
    tags: ["modern", "stadium", "young professionals", "luxury apartments", "convenient"],
    climate: {
      avgTemp: 18,
      rainyDays: 70,
      windyDays: 110,
      bestMonths: ["Nov", "Dec", "Jan", "Feb", "Mar"]
    },
    transport: {
      uberAvg: 15,
      publicTransport: ["MyCiTi bus", "Stadium shuttle"],
      parking: "Secure parking available, street parking limited"
    }
  },

  {
    name: "Hout Bay",
    borough: "Atlantic Seaboard",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -34.0483, lng: 18.3517 },
    demographics: {
      population: 24000,
      medianAge: 40,
      medianIncome: 395000,
      ethnicComposition: {
        white: 35, black: 25, coloured: 35, indian: 3, other: 2
      },
      educationLevel: {
        matric: 82, diploma: 38, degree: 45
      }
    },
    housing: {
      avgRent: 28000,
      rentalRange: { min: 12000, max: 75000 },
      rentByBedroom: {
        studio: { 
          min: 12000, max: 16000, avg: 14000,
          typical: "Beach cottage studio or apartment with mountain views",
          features: ["Mountain views", "Beach proximity", "Garden", "Parking"]
        },
        oneBed: { 
          min: 15000, max: 25000, avg: 20000,
          typical: "Cozy apartment or cottage with harbour/mountain views",
          features: ["Harbour views", "Garden access", "Pet-friendly", "Fireplace"]
        },
        twoBed: { 
          min: 20000, max: 40000, avg: 30000,
          typical: "Family cottage or apartment with sea glimpses",
          features: ["Sea glimpses", "Garden", "2 bathrooms", "Valley setting"]
        },
        threeBed: { 
          min: 28000, max: 55000, avg: 41500,
          typical: "Spacious house or cottage with mountain backdrop",
          features: ["Mountain views", "Private garden", "Fireplace", "Study"]
        },
        fourBed: { 
          min: 45000, max: 75000, avg: 60000,
          typical: "Large family house with sea views and garden",
          features: ["Sea views", "Large garden", "Multiple bathrooms", "Staff room"]
        }
      },
      avgSalePrice: 3800000,
      rentalAvailability: 18,
      homeOwnershipRate: 72,
      pricePerSqM: 32000,
      rentalYield: 6.5,
      propertyTypes: {
        houses: 65, apartments: 30, cottages: 5
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 1800
      }
    },
    safety: {
      crimeRate: 2.8,
      safetyScore: 7.1,
      crimeTypes: { violent: 6, property: 22, drug: 8, other: 10 },
      securityFeatures: ["Neighbourhood watch", "Some gated areas", "Mountain rescue"]
    },
    amenities: {
      restaurants: 35,
      schools: 8,
      parks: 6,
      transitScore: 45,
      walkabilityScore: 72,
      bikeScore: 68,
      groceryStores: 12,
      hospitals: 1,
      beaches: 2,
      entertainment: ["Harbour", "Bay Harbour Market", "Chapman's Peak"],
      shopping: ["Bay Harbour Market", "Local craft shops"]
    },
    description: "Scenic fishing village surrounded by mountains with relaxed lifestyle, popular with families and creative professionals.",
    tags: ["scenic", "village", "mountains", "fishing", "family-friendly", "artistic community"],
    climate: {
      avgTemp: 17,
      rainyDays: 78,
      windyDays: 95,
      bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb", "Mar"]
    },
    transport: {
      uberAvg: 35,
      publicTransport: ["Limited bus service", "Taxi routes"],
      parking: "Generally good parking availability"
    }
  },

  // CITY BOWL & FRINGE
  {
    name: "Cape Town Central",
    borough: "City Bowl",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.9249, lng: 18.4241 },
    demographics: {
      population: 18500,
      medianAge: 32,
      medianIncome: 385000,
      ethnicComposition: {
        white: 35, black: 25, coloured: 32, indian: 6, other: 2
      },
      educationLevel: {
        matric: 85, diploma: 45, degree: 58
      }
    },
    housing: {
      avgRent: 24000, // Weighted average based on bedroom distribution
      rentalRange: { min: 12000, max: 85000 },
      rentByBedroom: {
        studio: { 
          min: 12000, max: 18000, avg: 15000,
          typical: "Modern studio in converted building or new development",
          features: ["City views", "Building security", "Gym", "Rooftop"]
        },
        oneBed: { 
          min: 15000, max: 32000, avg: 23500,
          typical: "Urban apartment with city/mountain views",
          features: ["City views", "Modern kitchen", "Security", "Balcony"]
        },
        twoBed: { 
          min: 22000, max: 50000, avg: 36000,
          typical: "Spacious apartment in high-rise or converted building",
          features: ["Mountain/city views", "2 bathrooms", "Modern finishes", "Pool"]
        },
        threeBed: { 
          min: 35000, max: 70000, avg: 52500,
          typical: "Large apartment or loft-style unit, rare availability",
          features: ["Loft-style", "Multiple balconies", "Premium location", "Views"]
        },
        fourBed: { 
          min: 55000, max: 85000, avg: 70000,
          typical: "Penthouse or luxury apartment, very limited stock",
          features: ["Penthouse", "360° views", "Premium finishes", "Private terrace"]
        }
      },
      avgSalePrice: 3200000,
      rentalAvailability: 22,
      homeOwnershipRate: 45,
      pricePerSqM: 38000,
      rentalYield: 7.2,
      propertyTypes: {
        apartments: 95, lofts: 3, penthouses: 2
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 2400
      }
    },
    safety: {
      crimeRate: 16.8,
      safetyScore: 3.5,
      crimeTypes: { violent: 47, property: 85, drug: 25, other: 45 },
      securityFeatures: ["Building security", "CCTV", "Access control", "Security companies"]
    },
    amenities: {
      restaurants: 185,
      schools: 12,
      parks: 6,
      transitScore: 95,
      walkabilityScore: 92,
      bikeScore: 78,
      groceryStores: 25,
      hospitals: 4,
      beaches: 0,
      entertainment: ["Long Street", "V&A Waterfront", "Company's Garden"],
      shopping: ["St George's Mall", "Grand Parade", "Local markets"]
    },
    description: "Central business district with urban lifestyle, excellent transport links but higher crime rates.",
    tags: ["CBD", "urban", "nightlife", "business", "transport hub", "high crime"],
    climate: {
      avgTemp: 18,
      rainyDays: 72,
      windyDays: 105,
      bestMonths: ["Nov", "Dec", "Jan", "Feb"]
    },
    transport: {
      uberAvg: 5,
      publicTransport: ["MyCiTi", "Golden Arrow", "Metrorail", "Taxis"],
      parking: "Secure parking essential, expensive"
    }
  },

  {
    name: "Woodstock",
    borough: "City Bowl East",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.9258, lng: 18.4558 },
    demographics: {
      population: 16500,
      medianAge: 31,
      medianIncome: 295000,
      ethnicComposition: {
        white: 28, black: 22, coloured: 42, indian: 5, other: 3
      },
      educationLevel: {
        matric: 78, diploma: 42, degree: 48
      }
    },
    housing: {
      avgRent: 18500,
      rentalRange: { min: 8000, max: 45000 },
      rentByBedroom: {
        studio: { 
          min: 8000, max: 12000, avg: 10000,
          typical: "Industrial loft conversion or modern studio",
          features: ["Industrial character", "High ceilings", "Exposed brick", "Security"]
        },
        oneBed: { 
          min: 10000, max: 18000, avg: 14000,
          typical: "Converted warehouse apartment or new development",
          features: ["Loft-style", "Modern kitchen", "Security", "Character features"]
        },
        twoBed: { 
          min: 14000, max: 28000, avg: 21000,
          typical: "Spacious loft or apartment in trendy development",
          features: ["Open plan", "Industrial finishes", "Balcony", "Pool"]
        },
        threeBed: { 
          min: 20000, max: 38000, avg: 29000,
          typical: "Large apartment or house in emerging area",
          features: ["Family space", "Garden", "Parking", "Character home"]
        },
        fourBed: { 
          min: 30000, max: 45000, avg: 37500,
          typical: "Large family house or converted industrial building",
          features: ["Large spaces", "Garden", "Multiple parking", "Workshop space"]
        }
      },
      avgSalePrice: 2400000,
      rentalAvailability: 25,
      homeOwnershipRate: 62,
      pricePerSqM: 28000,
      rentalYield: 8.2,
      propertyTypes: {
        apartments: 55, lofts: 25, houses: 20
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 1600
      }
    },
    safety: {
      crimeRate: 3.8,
      safetyScore: 6.5,
      crimeTypes: { violent: 8, property: 28, drug: 12, other: 15 },
      securityFeatures: ["Building security", "Neighbourhood watch", "CCTV"]
    },
    amenities: {
      restaurants: 45,
      schools: 6,
      parks: 4,
      transitScore: 72,
      walkabilityScore: 85,
      bikeScore: 88,
      groceryStores: 18,
      hospitals: 1,
      beaches: 0,
      entertainment: ["Neighbourgoods Market", "Art galleries", "Craft breweries"],
      shopping: ["Neighbourgoods Market", "Local design shops"]
    },
    description: "Trendy creative district with industrial character, popular with young professionals and artists. Rising property values.",
    tags: ["trendy", "creative", "industrial", "gentrifying", "arts district", "young professionals"],
    climate: {
      avgTemp: 17,
      rainyDays: 75,
      windyDays: 98,
      bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb"]
    },
    transport: {
      uberAvg: 12,
      publicTransport: ["MyCiTi", "Taxi routes", "Train line"],
      parking: "Street parking available, some secure options"
    }
  },

  // SOUTHERN SUBURBS
  {
    name: "Rondebosch",
    borough: "Southern Suburbs",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.9671, lng: 18.4844 },
    demographics: {
      population: 14800,
      medianAge: 35,
      medianIncome: 380000,
      ethnicComposition: {
        white: 38, black: 22, coloured: 32, indian: 6, other: 2
      },
      educationLevel: {
        matric: 92, diploma: 55, degree: 64
      }
    },
    housing: {
      avgRent: 22000,
      rentalRange: { min: 10000, max: 45000 },
      rentByBedroom: {
        studio: { 
          min: 10000, max: 14000, avg: 12000,
          typical: "Student-friendly apartment near UCT campus",
          features: ["UCT proximity", "Student-oriented", "Basic finishes", "Shared facilities"]
        },
        oneBed: { 
          min: 12000, max: 20000, avg: 16000,
          typical: "Garden cottage or apartment, leafy setting",
          features: ["Garden setting", "Character features", "Parking", "UCT access"]
        },
        twoBed: { 
          min: 16000, max: 30000, avg: 23000,
          typical: "Family apartment or house with garden",
          features: ["Garden", "Family-friendly", "Good schools", "Tree-lined streets"]
        },
        threeBed: { 
          min: 22000, max: 40000, avg: 31000,
          typical: "Spacious family house with large garden",
          features: ["Large garden", "Multiple bathrooms", "Study", "Family area"]
        },
        fourBed: { 
          min: 32000, max: 45000, avg: 38500,
          typical: "Large family home, academic area character",
          features: ["Academic area", "Large spaces", "Multiple parking", "Established garden"]
        }
      },
      avgSalePrice: 3200000,
      rentalAvailability: 18,
      homeOwnershipRate: 72,
      pricePerSqM: 32000,
      rentalYield: 6.5,
      propertyTypes: {
        houses: 70, apartments: 25, cottages: 5
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 1900
      }
    },
    safety: {
      crimeRate: 2.8,
      safetyScore: 7.5,
      crimeTypes: { violent: 5, property: 22, drug: 4, other: 8 },
      securityFeatures: ["Neighbourhood watch", "Campus security nearby", "CCTV"]
    },
    amenities: {
      restaurants: 25,
      schools: 8,
      parks: 4,
      transitScore: 78,
      walkabilityScore: 85,
      bikeScore: 72,
      groceryStores: 12,
      hospitals: 1,
      beaches: 0,
      entertainment: ["UCT campus", "Baxter Theatre", "Rugby grounds"],
      shopping: ["Cavendish Square nearby", "Local shops"]
    },
    description: "Historic academic suburb home to University of Cape Town, with tree-lined streets and intellectual atmosphere.",
    tags: ["academic", "university", "historic", "leafy", "family-friendly", "students"],
    climate: {
      avgTemp: 17,
      rainyDays: 78,
      windyDays: 95,
      bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb", "Mar"]
    },
    transport: {
      uberAvg: 20,
      publicTransport: ["UCT shuttle", "MyCiTi", "Train"],
      parking: "Generally good availability"
    }
  },

  {
    name: "Claremont",
    borough: "Southern Suburbs",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.9847, lng: 18.4647 },
    demographics: {
      population: 18500,
      medianAge: 38,
      medianIncome: 420000,
      ethnicComposition: {
        white: 42, black: 18, coloured: 32, indian: 6, other: 2
      },
      educationLevel: {
        matric: 88, diploma: 45, degree: 52
      }
    },
    housing: {
      avgRent: 21000,
      rentalRange: { min: 8000, max: 48000 },
      rentByBedroom: {
        studio: { 
          min: 8000, max: 12000, avg: 10000,
          typical: "Compact apartment near shopping and transport",
          features: ["Transport hub", "Shopping access", "Security", "Basic amenities"]
        },
        oneBed: { 
          min: 11000, max: 18000, avg: 14500,
          typical: "Garden flat or apartment with suburban character",
          features: ["Garden access", "Suburban feel", "Good transport", "Shopping nearby"]
        },
        twoBed: { 
          min: 15000, max: 28000, avg: 21500,
          typical: "Family apartment or house near excellent schools",
          features: ["School proximity", "Family area", "Garden", "Transport links"]
        },
        threeBed: { 
          min: 20000, max: 38000, avg: 29000,
          typical: "Large family house with established garden and study",
          features: ["Large garden", "Study room", "Multiple bathrooms", "Family area", "Good schools"],
          description: "Popular with families due to excellent schools like Reddam House and Springfield Convent. Most properties have mature gardens and off-street parking.",
          availability: "Good - 25-30 available monthly",
          popularAreas: ["Main Road corridor", "Protea Road", "Newlands Avenue"]
        },
        fourBed: { 
          min: 32000, max: 48000, avg: 40000,
          typical: "Executive family home in prime school catchment area",
          features: ["Double garage", "Large garden", "Study", "Multiple living areas", "Staff accommodation"],
          description: "Spacious homes ideal for large families. Many feature pools and are within walking distance of top schools.",
          availability: "Limited - 8-12 available monthly",
          popularAreas: ["Upper Claremont", "Harfield Village borders"]
        }
      },
      avgSalePrice: 3400000,
      rentalAvailability: 20,
      homeOwnershipRate: 68,
      pricePerSqM: 34000,
      rentalYield: 6.8,
      propertyTypes: {
        houses: 65, apartments: 30, flats: 5
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet", "Garden service"],
        avgUtilityCost: 2100
      }
    },
    safety: {
      crimeRate: 2.4,
      safetyScore: 7.8,
      crimeTypes: { violent: 4, property: 18, drug: 3, other: 7 },
      securityFeatures: ["Neighbourhood watch", "CCTV", "Rapid response", "School patrols"]
    },
    amenities: {
      restaurants: 35,
      schools: 12,
      parks: 5,
      transitScore: 82,
      walkabilityScore: 88,
      bikeScore: 75,
      groceryStores: 18,
      hospitals: 2,
      beaches: 0,
      entertainment: ["Cavendish Square", "Claremont Club", "Rugby Club"],
      shopping: ["Cavendish Square", "Main Road shops", "Harfield Village"]
    },
    description: "Family-oriented suburb with excellent schools and shopping. Major transport hub with train station and strong community feel.",
    tags: ["family-friendly", "schools", "transport hub", "shopping", "community"],
    climate: {
      avgTemp: 17,
      rainyDays: 82,
      windyDays: 88,
      bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb", "Mar"]
    },
    transport: {
      uberAvg: 18,
      publicTransport: ["Train station", "MyCiTi", "Multiple taxi routes"],
      parking: "Good availability, some metered zones"
    }
  },

  {
    name: "Newlands",
    borough: "Southern Suburbs",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.9833, lng: 18.5167 },
    demographics: {
      population: 8500,
      medianAge: 45,
      medianIncome: 580000,
      ethnicComposition: {
        white: 65, black: 12, coloured: 18, indian: 4, other: 1
      },
      educationLevel: {
        matric: 94, diploma: 38, degree: 72
      }
    },
    housing: {
      avgRent: 32000,
      rentalRange: { min: 15000, max: 75000 },
      rentByBedroom: {
        studio: { 
          min: 15000, max: 20000, avg: 17500,
          typical: "Garden cottage or converted space in leafy setting",
          features: ["Garden setting", "Mountain views", "Character features", "Parking"],
          description: "Limited studio options, mostly garden cottages on large properties. Very peaceful with mountain views.",
          availability: "Very limited - 3-5 available monthly",
          popularAreas: ["Kirstenbosch borders", "Dean Street", "Paradise Road"]
        },
        oneBed: { 
          min: 18000, max: 28000, avg: 23000,
          typical: "Garden flat or cottage with mountain views and mature garden",
          features: ["Mountain views", "Garden access", "Character property", "Quiet location"],
          description: "Charming garden flats in established properties. Most have access to beautiful gardens and mountain views.",
          availability: "Limited - 8-12 available monthly",
          popularAreas: ["Main Road", "Wilderness Road", "Union Avenue"]
        },
        twoBed: { 
          min: 25000, max: 45000, avg: 35000,
          typical: "Spacious apartment or cottage with gardens and mountain backdrop",
          features: ["Mountain views", "Large garden", "2 bathrooms", "Character features", "Parking"],
          description: "Family-friendly properties with access to excellent outdoor spaces. Many have been recently renovated.",
          availability: "Moderate - 15-20 available monthly",
          popularAreas: ["Newlands Avenue", "Main Road", "Klaasens Road"]
        },
        threeBed: { 
          min: 35000, max: 60000, avg: 47500,
          typical: "Large family house with extensive gardens and mountain views",
          features: ["Extensive gardens", "Mountain views", "Study", "Multiple living areas", "Double garage"],
          description: "Beautiful family homes with large gardens. Close to Kirstenbosch and excellent schools.",
          availability: "Good - 18-25 available monthly",
          popularAreas: ["Upper Newlands", "Paradise Road", "Wilderness Road"]
        },
        fourBed: { 
          min: 50000, max: 75000, avg: 62500,
          typical: "Executive family home with pool and mountain views",
          features: ["Swimming pool", "Mountain views", "Staff quarters", "Wine cellar", "Large entertainment areas"],
          description: "Luxury family homes with extensive outdoor areas. Many feature pools and are close to prestigious schools.",
          availability: "Limited - 8-12 available monthly",
          popularAreas: ["Vineyard Road", "Paradise Road", "Upper Newlands"]
        }
      },
      avgSalePrice: 5200000,
      rentalAvailability: 12,
      homeOwnershipRate: 85,
      pricePerSqM: 42000,
      rentalYield: 5.8,
      propertyTypes: {
        houses: 80, cottages: 15, apartments: 5
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet", "Garden service", "Pool maintenance"],
        avgUtilityCost: 2800
      }
    },
    safety: {
      crimeRate: 1.8,
      safetyScore: 8.5,
      crimeTypes: { violent: 2, property: 12, drug: 1, other: 4 },
      securityFeatures: ["Neighbourhood watch", "Private security", "CCTV", "Access control"]
    },
    amenities: {
      restaurants: 18,
      schools: 6,
      parks: 8,
      transitScore: 68,
      walkabilityScore: 75,
      bikeScore: 82,
      groceryStores: 8,
      hospitals: 1,
      beaches: 0,
      entertainment: ["Kirstenbosch Gardens", "Newlands Rugby Stadium", "Cricket Ground"],
      shopping: ["Cavendish Square nearby", "Local village shops"]
    },
    description: "Prestigious leafy suburb with large properties, mountain views, and proximity to Kirstenbosch. Popular with affluent families.",
    tags: ["prestigious", "leafy", "mountain views", "large properties", "family-oriented"],
    climate: {
      avgTemp: 16,
      rainyDays: 88,
      windyDays: 75,
      bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb", "Mar", "Apr"]
    },
    transport: {
      uberAvg: 22,
      publicTransport: ["Train station", "Limited bus routes"],
      parking: "Excellent availability"
    }
  },

  {
    name: "Observatory",
    borough: "Southern Suburbs",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.9344, lng: 18.4731 },
    demographics: {
      population: 16800,
      medianAge: 28,
      medianIncome: 285000,
      ethnicComposition: {
        white: 32, black: 28, coloured: 35, indian: 3, other: 2
      },
      educationLevel: {
        matric: 82, diploma: 48, degree: 58
      }
    },
    housing: {
      avgRent: 16000,
      rentalRange: { min: 6000, max: 35000 },
      rentByBedroom: {
        studio: { 
          min: 6000, max: 9000, avg: 7500,
          typical: "Converted space or small apartment, student-friendly",
          features: ["Student area", "Affordable", "Character building", "Transport links"],
          description: "Budget-friendly options popular with students and young professionals. Many in converted Victorian houses.",
          availability: "High - 35-45 available monthly",
          popularAreas: ["Lower Main Road", "Station Road", "Trill Road"]
        },
        oneBed: { 
          min: 8000, max: 15000, avg: 11500,
          typical: "Character apartment or garden flat in Victorian house",
          features: ["Character features", "High ceilings", "Garden access", "Student-friendly"],
          description: "Mix of garden flats and apartments in converted houses. Popular with UCT students and young professionals.",
          availability: "High - 50-65 available monthly",
          popularAreas: ["Main Road", "Mill Street", "Trill Road"]
        },
        twoBed: { 
          min: 12000, max: 22000, avg: 17000,
          typical: "Spacious apartment or house with character features",
          features: ["Character features", "Garden", "High ceilings", "Off-street parking"],
          description: "Good family options with character. Many properties have been renovated while maintaining original features.",
          availability: "Good - 30-40 available monthly",
          popularAreas: ["Upper Observatory", "Trill Road", "Mill Street"]
        },
        threeBed: { 
          min: 18000, max: 30000, avg: 24000,
          typical: "Large character house with garden and original features",
          features: ["Character house", "Large garden", "High ceilings", "Multiple living areas"],
          description: "Spacious family homes with original features. Many have large gardens and are popular with families.",
          availability: "Moderate - 20-25 available monthly",
          popularAreas: ["Upper Observatory", "Milner Road", "Trill Road"]
        },
        fourBed: { 
          min: 25000, max: 35000, avg: 30000,
          typical: "Large Victorian house with extensive garden",
          features: ["Victorian architecture", "Large garden", "Period features", "Multiple parking"],
          description: "Beautiful large homes with period features. Often shared by students or used by large families.",
          availability: "Limited - 8-12 available monthly",
          popularAreas: ["Milner Road", "Upper Observatory", "Trill Road"]
        }
      },
      avgSalePrice: 2200000,
      rentalAvailability: 28,
      homeOwnershipRate: 55,
      pricePerSqM: 25000,
      rentalYield: 8.5,
      propertyTypes: {
        houses: 70, apartments: 25, flats: 5
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 1400
      }
    },
    safety: {
      crimeRate: 4.2,
      safetyScore: 6.2,
      crimeTypes: { violent: 8, property: 32, drug: 15, other: 18 },
      securityFeatures: ["Community watch", "Student patrols", "Some CCTV"]
    },
    amenities: {
      restaurants: 28,
      schools: 4,
      parks: 3,
      transitScore: 85,
      walkabilityScore: 92,
      bikeScore: 88,
      groceryStores: 15,
      hospitals: 1,
      beaches: 0,
      entertainment: ["Observatory bars", "Live music venues", "Art spaces"],
      shopping: ["Main Road shops", "Local markets"]
    },
    description: "Bohemian student area with character Victorian houses, vibrant nightlife, and strong community feel. Popular with artists and students.",
    tags: ["student area", "bohemian", "character houses", "nightlife", "affordable"],
    climate: {
      avgTemp: 17,
      rainyDays: 78,
      windyDays: 92,
      bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb"]
    },
    transport: {
      uberAvg: 15,
      publicTransport: ["Train station", "Multiple taxi routes", "UCT shuttle"],
      parking: "Street parking available"
    }
  },

  // NORTHERN SUBURBS
  {
    name: "Bellville",
    borough: "Northern Suburbs",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.8886, lng: 18.6336 },
    demographics: {
      population: 52000,
      medianAge: 34,
      medianIncome: 285000,
      ethnicComposition: {
        white: 28, black: 22, coloured: 45, indian: 4, other: 1
      },
      educationLevel: {
        matric: 78, diploma: 42, degree: 35
      }
    },
    housing: {
      avgRent: 14500,
      rentalRange: { min: 6000, max: 28000 },
      rentByBedroom: {
        studio: { 
          min: 6000, max: 8500, avg: 7250,
          typical: "Modern apartment in new development near university",
          features: ["University proximity", "Modern finishes", "Security", "Transport links"],
          description: "Popular with Stellenbosch University students. Many new developments with modern amenities.",
          availability: "High - 40-50 available monthly",
          popularAreas: ["Central Bellville", "University area", "Oude Westhof"]
        },
        oneBed: { 
          min: 8000, max: 13000, avg: 10500,
          typical: "Apartment or townhouse with modern amenities",
          features: ["Modern amenities", "Security", "Parking", "Shopping proximity"],
          description: "Good mix of apartments and townhouses. Popular with young professionals working in the northern suburbs.",
          availability: "High - 60-75 available monthly",
          popularAreas: ["Bellville CBD", "Tygervalley area", "Oude Westhof"]
        },
        twoBed: { 
          min: 11000, max: 18000, avg: 14500,
          typical: "Family townhouse or apartment with garden access",
          features: ["Garden access", "Family-friendly", "Good schools", "Shopping centers"],
          description: "Family-oriented properties with good access to schools and shopping. Many in secure complexes.",
          availability: "High - 70-85 available monthly",
          popularAreas: ["Rosendal", "De Tijger", "Bellville South"]
        },
        threeBed: { 
          min: 15000, max: 24000, avg: 19500,
          typical: "Spacious family home with garden and double garage",
          features: ["Double garage", "Garden", "Family area", "Good schools", "Security"],
          description: "Solid family homes popular with middle-class families. Good value for money compared to southern suburbs.",
          availability: "Good - 45-55 available monthly",
          popularAreas: ["Bellville South", "De Tijger", "Rosendal"]
        },
        fourBed: { 
          min: 20000, max: 28000, avg: 24000,
          typical: "Large family home with pool and entertainment area",
          features: ["Swimming pool", "Entertainment area", "Double garage", "Large garden"],
          description: "Spacious family homes with good entertainment areas. Popular with growing families.",
          availability: "Moderate - 25-35 available monthly",
          popularAreas: ["Bellville South", "De Tijger", "Boston"]
        }
      },
      avgSalePrice: 1850000,
      rentalAvailability: 35,
      homeOwnershipRate: 62,
      pricePerSqM: 18500,
      rentalYield: 8.8,
      propertyTypes: {
        houses: 60, townhouses: 25, apartments: 15
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 1600
      }
    },
    safety: {
      crimeRate: 3.5,
      safetyScore: 6.8,
      crimeTypes: { violent: 7, property: 26, drug: 12, other: 15 },
      securityFeatures: ["Complex security", "Neighbourhood watch", "CCTV"]
    },
    amenities: {
      restaurants: 45,
      schools: 18,
      parks: 8,
      transitScore: 78,
      walkabilityScore: 72,
      bikeScore: 65,
      groceryStores: 25,
      hospitals: 3,
      beaches: 0,
      entertainment: ["Tygervalley Centre", "Willowbridge Centre", "University campus"],
      shopping: ["Tygervalley Centre", "Willowbridge", "Bellville Mall"]
    },
    description: "Major northern suburbs hub with university, shopping centers, and good transport links. Popular with families and students.",
    tags: ["university town", "family-friendly", "shopping", "transport hub", "affordable"],
    climate: {
      avgTemp: 18,
      rainyDays: 72,
      windyDays: 85,
      bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb", "Mar"]
    },
    transport: {
      uberAvg: 25,
      publicTransport: ["Train station", "Bus routes", "University transport"],
      parking: "Good availability"
    }
  },

  {
    name: "Durbanville",
    borough: "Northern Suburbs",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -33.8322, lng: 18.6486 },
    demographics: {
      population: 54000,
      medianAge: 36,
      medianIncome: 485000,
      ethnicComposition: {
        white: 62, black: 8, coloured: 25, indian: 4, other: 1
      },
      educationLevel: {
        matric: 92, diploma: 48, degree: 58
      }
    },
    housing: {
      avgRent: 18500,
      rentalRange: { min: 8000, max: 35000 },
      rentByBedroom: {
        studio: { 
          min: 8000, max: 11000, avg: 9500,
          typical: "Modern apartment in new development with wine country views",
          features: ["Wine country views", "Modern finishes", "Security", "Pool"],
          description: "Limited studio options, mostly in new developments. Popular with young professionals.",
          availability: "Limited - 8-12 available monthly",
          popularAreas: ["Durbanville Hills", "Central Durbanville", "De Bron"]
        },
        oneBed: { 
          min: 10000, max: 16000, avg: 13000,
          typical: "Apartment or townhouse with mountain views and wine country setting",
          features: ["Mountain views", "Wine country", "Security", "Modern amenities"],
          description: "Good selection of modern apartments and townhouses. Popular with young couples and professionals.",
          availability: "Good - 35-45 available monthly",
          popularAreas: ["Durbanville Hills", "Wellies", "De Bron"]
        },
        twoBed: { 
          min: 13000, max: 22000, avg: 17500,
          typical: "Family townhouse or apartment with wine farm views",
          features: ["Wine farm views", "Family-friendly", "Good schools", "Security estate"],
          description: "Popular family options in secure estates. Many with wine farm views and access to excellent schools.",
          availability: "Good - 50-65 available monthly",
          popularAreas: ["Durbanville Hills", "Wellies", "Sonstraal Heights"]
        },
        threeBed: { 
          min: 18000, max: 28000, avg: 23000,
          typical: "Spacious family home in security estate with mountain views",
          features: ["Security estate", "Mountain views", "Double garage", "Garden", "Family area"],
          description: "Excellent family homes in well-established security estates. Popular with families for safety and schools.",
          availability: "High - 65-80 available monthly",
          popularAreas: ["Sonstraal Heights", "Durbanville Hills", "Vierlanden"]
        },
        fourBed: { 
          min: 24000, max: 35000, avg: 29500,
          typical: "Large family home with pool and wine country views",
          features: ["Swimming pool", "Wine country views", "Entertainment area", "Double garage", "Study"],
          description: "Spacious family homes with excellent entertainment areas. Many with pools and mountain views.",
          availability: "Good - 40-50 available monthly",
          popularAreas: ["Sonstraal Heights", "Durbanville Hills", "Wellies"]
        }
      },
      avgSalePrice: 2650000,
      rentalAvailability: 25,
      homeOwnershipRate: 78,
      pricePerSqM: 22000,
      rentalYield: 7.2,
      propertyTypes: {
        houses: 70, townhouses: 25, apartments: 5
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet", "Estate levies"],
        avgUtilityCost: 2200
      }
    },
    safety: {
      crimeRate: 1.8,
      safetyScore: 8.8,
      crimeTypes: { violent: 2, property: 12, drug: 2, other: 5 },
      securityFeatures: ["Estate security", "Access control", "24/7 patrols", "CCTV"]
    },
    amenities: {
      restaurants: 35,
      schools: 15,
      parks: 12,
      transitScore: 55,
      walkabilityScore: 68,
      bikeScore: 72,
      groceryStores: 18,
      hospitals: 2,
      beaches: 0,
      entertainment: ["Wine farms", "Durbanville Rose Garden", "Golf courses"],
      shopping: ["Durbanville Centre", "Willowbridge nearby"]
    },
    description: "Upmarket family suburb in wine country with excellent schools and security estates. Popular with affluent families.",
    tags: ["wine country", "family-friendly", "security estates", "excellent schools", "upmarket"],
    climate: {
      avgTemp: 17,
      rainyDays: 75,
      windyDays: 78,
      bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb", "Mar", "Apr"]
    },
    transport: {
      uberAvg: 35,
      publicTransport: ["Limited bus service"],
      parking: "Excellent availability"
    }
  },

  // SOUTHERN PENINSULA
  {
    name: "Muizenberg",
    borough: "Southern Peninsula",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -34.1033, lng: 18.4667 },
    demographics: {
      population: 13500,
      medianAge: 39,
      medianIncome: 285000,
      ethnicComposition: {
        white: 28, black: 15, coloured: 52, indian: 4, other: 1
      },
      educationLevel: {
        matric: 75, diploma: 35, degree: 38
      }
    },
    housing: {
      avgRent: 16500,
      rentalRange: { min: 7000, max: 45000 },
      rentByBedroom: {
        studio: { 
          min: 7000, max: 11000, avg: 9000,
          typical: "Beach cottage studio or apartment with sea views",
          features: ["Sea views", "Beach proximity", "Surfing culture", "Character building"],
          description: "Popular with surfers and beach lovers. Many in converted beach cottages with character.",
          availability: "Moderate - 15-20 available monthly",
          popularAreas: ["Beachfront", "Main Road", "Atlantic Road"]
        },
        oneBed: { 
          min: 9000, max: 18000, avg: 13500,
          typical: "Beach apartment or cottage with ocean views",
          features: ["Ocean views", "Beach access", "Character features", "Surfing proximity"],
          description: "Mix of apartments and beach cottages. Very popular with surfers and beach lifestyle enthusiasts.",
          availability: "Good - 25-35 available monthly",
          popularAreas: ["Beachfront", "Surfers Corner", "Main Road"]
        },
        twoBed: { 
          min: 13000, max: 25000, avg: 19000,
          typical: "Family beach house or apartment with sea views",
          features: ["Sea views", "Beach access", "Family-friendly", "Garden", "Character"],
          description: "Family-friendly properties close to the beach. Many with character features and sea views.",
          availability: "Good - 30-40 available monthly",
          popularAreas: ["Atlantic Road", "Beachfront", "Steenberg area"]
        },
        threeBed: { 
          min: 18000, max: 35000, avg: 26500,
          typical: "Large beach house with sea views and garden",
          features: ["Sea views", "Large garden", "Beach proximity", "Character house", "Multiple bathrooms"],
          description: "Spacious family homes perfect for beach lifestyle. Many with large gardens and sea views.",
          availability: "Moderate - 20-25 available monthly",
          popularAreas: ["Atlantic Road", "Steenberg", "Mountain Road"]
        },
        fourBed: { 
          min: 28000, max: 45000, avg: 36500,
          typical: "Large beach house with panoramic sea views",
          features: ["Panoramic sea views", "Large entertainment areas", "Beach access", "Pool", "Garden"],
          description: "Premium beach houses with excellent entertainment areas. Popular for holiday rentals.",
          availability: "Limited - 8-12 available monthly",
          popularAreas: ["Atlantic Road", "Mountain slopes", "Steenberg borders"]
        }
      },
      avgSalePrice: 2400000,
      rentalAvailability: 22,
      homeOwnershipRate: 58,
      pricePerSqM: 24000,
      rentalYield: 7.8,
      propertyTypes: {
        houses: 75, apartments: 20, cottages: 5
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 1700
      }
    },
    safety: {
      crimeRate: 3.2,
      safetyScore: 6.8,
      crimeTypes: { violent: 6, property: 24, drug: 8, other: 12 },
      securityFeatures: ["Community policing", "Beach patrol", "CCTV"]
    },
    amenities: {
      restaurants: 25,
      schools: 6,
      parks: 4,
      transitScore: 65,
      walkabilityScore: 85,
      bikeScore: 92,
      groceryStores: 12,
      hospitals: 1,
      beaches: 2,
      entertainment: ["Surfing", "Beach activities", "Pavilion"],
      shopping: ["Main Road shops", "Beach markets"]
    },
    description: "Historic beach town with colorful beach huts, excellent surfing, and relaxed coastal lifestyle. Popular with surfers and families.",
    tags: ["beach town", "surfing", "colorful beach huts", "coastal lifestyle", "family-friendly"],
    climate: {
      avgTemp: 17,
      rainyDays: 85,
      windyDays: 125,
      bestMonths: ["Nov", "Dec", "Jan", "Feb", "Mar"]
    },
    transport: {
      uberAvg: 35,
      publicTransport: ["Train line", "Limited bus service"],
      parking: "Generally good, busy in summer"
    }
  },
  {
    name: "Constantia",
    borough: "Southern Suburbs",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -34.0142, lng: 18.4239 },
    demographics: {
      population: 8500,
      medianAge: 48,
      medianIncome: 1200000, // Ultra-luxury area
      ethnicComposition: {
        white: 78, black: 5, coloured: 12, indian: 4, other: 1
      },
      educationLevel: {
        matric: 98, diploma: 52, degree: 78
      }
    },
    housing: {
      avgRent: 80000, // Average of 3-bed (57.5k), 4-bed (129.75k)
      rentRange: { min: 35000, max: 180000 },
      rentByBedroom: {
        studio: {
          min: 35000, max: 45000, avg: 40000,
          typical: "Ultra-luxury wine estate cottage or guest suite",
          features: ["Wine estate views", "Premium finishes", "Private entrance", "Estate amenities"],
          description: "Rare studio options, usually guest cottages on wine estates. Ultra-luxury finishes and mountain views.",
          availability: "Extremely limited - 1-2 available monthly",
          popularAreas: ["Groot Constantia", "Klein Constantia", "Buitenverwachting"]
        },
        oneBed: {
          min: 40000, max: 65000, avg: 52500,
          typical: "Luxury apartment or wine estate cottage with mountain views",
          features: ["Mountain views", "Wine estate setting", "Premium appliances", "Private garden"],
          description: "Exclusive properties on wine estates or luxury developments. Often with vineyard views.",
          availability: "Very limited - 2-3 available monthly",
          popularAreas: ["Groot Constantia", "Klein Constantia", "Steenberg"]
        },
        twoBed: {
          min: 55000, max: 95000, avg: 75000,
          typical: "Luxury house or apartment with vineyard and mountain views",
          features: ["Vineyard views", "Premium finishes", "Private garden", "Wine cellar"],
          description: "High-end properties with exceptional views. Many feature wine cellars and entertainment areas.",
          availability: "Limited - 3-4 available monthly",
          popularAreas: ["Klein Constantia", "Constantia Upper", "Steenberg"]
        },
        threeBed: {
          min: 70000, max: 120000, avg: 95000,
          typical: "Luxury family home or wine estate house with panoramic views",
          features: ["Panoramic views", "Wine estate", "Multiple entertainment areas", "Staff quarters"],
          description: "Premium family homes on wine estates. Often with pools, wine cellars, and mountain views.",
          availability: "Limited - 4-5 available monthly",
          popularAreas: ["Groot Constantia", "Klein Constantia", "Constantia Upper"]
        },
        fourBed: {
          min: 100000, max: 180000, avg: 140000,
          typical: "Ultra-luxury wine estate mansion with world-class views",
          features: ["World-class views", "Wine estate", "Pool", "Staff quarters", "Wine cellar"],
          description: "Exclusive wine estate properties with exceptional amenities. Often historic or architect-designed.",
          availability: "Extremely limited - 1-2 available monthly",
          popularAreas: ["Groot Constantia", "Klein Constantia", "Buitenverwachting"]
        }
      },
      avgSalePrice: 18000000, // Ultra-luxury wine estate properties
      rentalAvailability: 4, // Very limited luxury rental stock
      homeOwnershipRate: 92,
      pricePerSqM: 85000,
      rentalYield: 4.5, // Lower yield due to high property values
      propertyTypes: {
        wineEstateHomes: 60, luxuryHouses: 25, apartments: 10, cottages: 5
      },
      utilities: {
        included: ["Water", "Refuse", "Estate security", "Garden service"],
        excluded: ["Electricity", "Internet", "Wine estate fees"],
        avgUtilityCost: 4500
      }
    },
    safety: {
      crimeRate: 1.2,
      safetyScore: 9.1, // Highest safety score
      crimeTypes: {
        violent: 1, property: 8, drug: 1, other: 2
      }
    },
    amenities: {
      restaurants: 25, // High-end wine estate restaurants
      schools: 8, // Premium private schools
      parks: 12, // Wine estates and nature reserves
      transitScore: 35, // Limited public transport, car-dependent
      walkabilityScore: 65,
      bikeScore: 78,
      groceryStores: 8,
      hospitals: 1,
      beaches: 0
    },
    description: "Ultra-luxury wine estate area with mountain views, premium properties, and highest safety scores. Car-dependent but exclusive lifestyle.",
    tags: ["ultra-luxury", "wine estates", "mountain views", "exclusive", "premium", "safest area"],
    climate: {
      avgTemp: 16,
      rainyDays: 85,
      windyDays: 75, // More sheltered from wind
      bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb", "Mar", "Apr"],
      sunnyDays: 220,
      humidity: 65
    },
    transport: {
      uberAvg: 25, // Moderate distance from city center, but car-dependent area
      publicTransport: ["Limited bus service", "Wine tram (seasonal)", "Private shuttles"],
      parking: "Excellent private parking, estate security"
    }
  },
    // CAPE WINELANDS (from crime data)
 {
    name: "Stellenbosch",
    borough: "Cape Winelands",
    city: "Stellenbosch",
    state: "Western Cape",
    coordinates: { lat: -33.9321, lng: 18.8602 },
    demographics: {
      population: 27000,
      medianAge: 30, // University town
      medianIncome: 320000,
      ethnicComposition: {
        white: 45, black: 25, coloured: 25, indian: 3, other: 2
      },
      educationLevel: {
        matric: 88, diploma: 48, degree: 58 // University influence
      }
    },
    housing: {
      avgRent: 14500,
      rentalRange: { min: 6000, max: 28000 },
      rentByBedroom: {
        studio: {
          min: 6000, max: 9000, avg: 7500,
          typical: "Student accommodation or small apartment near university",
          features: ["University proximity", "Student-oriented", "Basic amenities", "Bike-friendly"],
          description: "Popular with students and young professionals. Many purpose-built student accommodations.",
          availability: "High - 40+ available monthly",
          popularAreas: ["Central Stellenbosch", "University area", "Dorp Street"]
        },
        oneBed: {
          min: 8000, max: 15000, avg: 11500,
          typical: "Apartment or cottage with wine country views",
          features: ["Wine country views", "Historic character", "University access", "Mountain views"],
          description: "Mix of historic cottages and modern apartments. Popular with young professionals and academics.",
          availability: "Moderate - 25+ available monthly",
          popularAreas: ["Historic center", "Dorp Street", "University area"]
        },
        twoBed: {
          min: 12000, max: 22000, avg: 17000,
          typical: "Family house or apartment with garden and mountain views",
          features: ["Mountain views", "Garden space", "Historic area", "Wine tourism access"],
          description: "Family-friendly properties with character. Many in historic areas with wine estate proximity.",
          availability: "Moderate - 20+ available monthly",
          popularAreas: ["Dorp Street", "Die Boord", "Paradyskloof"]
        },
        threeBed: {
          min: 16000, max: 26000, avg: 21000,
          typical: "Family home with wine estate views and historic character",
          features: ["Wine estate views", "Historic character", "Large garden", "University access"],
          description: "Established family homes, often with historic character. Popular with academic families.",
          availability: "Limited - 12+ available monthly",
          popularAreas: ["Die Boord", "Paradyskloof", "Welgevonden"]
        },
        fourBed: {
          min: 20000, max: 28000, avg: 24000,
          typical: "Large family home or wine estate property",
          features: ["Wine estate setting", "Large spaces", "Historic character", "Mountain views"],
          description: "Premium family homes, often on wine estates or in historic areas. Limited availability.",
          availability: "Limited - 6+ available monthly",
          popularAreas: ["Wine estates", "Paradyskloof", "Welgevonden"]
        }
      },
      avgSalePrice: 2200000,
      rentalAvailability: 15,
      homeOwnershipRate: 65,
      pricePerSqM: 22000,
      rentalYield: 7.8,
      propertyTypes: {
        houses: 50, apartments: 30, cottages: 15, studentAccommodation: 5
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 1800
      }
    },
    safety: {
      crimeRate: 5.5, // #18 nationally for total crimes
      safetyScore: 6.2,
      crimeTypes: {
        violent: 15, property: 35, drug: 12, other: 18
      }
    },
    amenities: {
      restaurants: 85, // Wine tourism
      schools: 15, // Including university
      parks: 12,
      transitScore: 45, // Limited public transport
      walkabilityScore: 78,
      bikeScore: 85, // Bike-friendly university town
      groceryStores: 18,
      hospitals: 2,
      beaches: 0
    },
    description: "Historic university town and wine capital with beautiful architecture and mountain views",
    tags: ["university", "wine tourism", "historic", "mountains", "afrikaans heritage", "student town"],
    climate: {
      avgTemp: 16,
      rainyDays: 82,
      windyDays: 75, // More sheltered from wind
      bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb", "Mar"],
      sunnyDays: 210,
      humidity: 68
    },
    transport: {
      uberAvg: 50, // Distance from Cape Town city center
      publicTransport: ["University shuttle", "Intercity bus", "Student transport"],
      parking: "Good availability, university parking permits required"
    }
  },

  // CAPE FLATS - Higher crime, lower income areas (from crime stats)
  {
    name: "Mitchells Plain",
    borough: "Cape Flats",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -34.0364, lng: 18.6292 },
    demographics: {
      population: 310000, // Large suburb
      medianAge: 28,
      medianIncome: 145000,
      ethnicComposition: {
        white: 2, black: 8, coloured: 87, indian: 2, other: 1
      },
      educationLevel: {
        matric: 67, diploma: 18, degree: 8
      }
    },
    housing: {
      avgRent: 8500,
      rentalRange: { min: 3500, max: 18000 },
      rentByBedroom: {
        studio: {
          min: 3500, max: 5500, avg: 4500,
          typical: "Backyard room or converted space in family home",
          features: ["Basic amenities", "Shared facilities", "Community access", "Transport links"],
          description: "Popular with students and young workers. Many in family backyards with shared facilities.",
          availability: "High - 50+ available monthly",
          popularAreas: ["Tafelsig", "Rocklands", "Westridge"]
        },
        oneBed: {
          min: 4500, max: 8000, avg: 6250,
          typical: "RDP house room or small flat in complex",
          features: ["Basic house", "Community", "Shopping access", "Transport hub"],
          description: "Mix of RDP house rooms and small flats. Popular area for first-time renters.",
          availability: "Very High - 80+ available monthly",
          popularAreas: ["Tafelsig", "Eastridge", "Westridge"]
        },
        twoBed: {
          min: 6000, max: 12000, avg: 9000,
          typical: "RDP house or apartment in residential complex",
          features: ["Family space", "Schools nearby", "Shopping centers", "Community facilities"],
          description: "Family accommodation, many RDP houses and apartment complexes. Good for young families.",
          availability: "High - 60+ available monthly",
          popularAreas: ["Eastridge", "Westridge", "Beacon Valley"]
        },
        threeBed: {
          min: 8000, max: 15000, avg: 11500,
          typical: "Family house or larger apartment",
          features: ["Family home", "Multiple rooms", "Yard space", "School access"],
          description: "Established family homes, often extended RDP houses. Popular with growing families.",
          availability: "Moderate - 30+ available monthly",
          popularAreas: ["Beacon Valley", "Eastridge", "Lentegeur"]
        },
        fourBed: {
          min: 12000, max: 18000, avg: 15000,
          typical: "Large family house or multi-generational home",
          features: ["Large family space", "Multiple facilities", "Established area", "Extended family"],
          description: "Larger family homes for established families. Often houses multiple generations.",
          availability: "Limited - 15+ available monthly",
          popularAreas: ["Beacon Valley", "Lentegeur"]
        }
      },
      avgSalePrice: 650000,
      rentalAvailability: 25,
      homeOwnershipRate: 78, // Many RDP houses
      pricePerSqM: 8500,
      rentalYield: 15.2, // High yield due to affordable property values
      propertyTypes: {
        rdpHouses: 60, apartments: 25, houses: 10, backyard: 5
      },
      utilities: {
        included: ["Basic electricity", "Water"],
        excluded: ["Prepaid electricity top-ups", "Internet"],
        avgUtilityCost: 1200
      }
    },
    safety: {
      crimeRate: 5.7, // #7 in total crimes nationally
      safetyScore: 4.2,
      crimeTypes: {
        violent: 28, property: 45, drug: 18, other: 25 // High across all categories
      }
    },
    amenities: {
      restaurants: 28,
      schools: 45,
      parks: 12,
      transitScore: 65, // Taxi routes
      walkabilityScore: 68,
      bikeScore: 45,
      groceryStores: 35,
      hospitals: 3,
      beaches: 0
    },
    description: "Large coloured community suburb with shopping centers and high crime rates",
    tags: ["working-class", "coloured community", "shopping", "high crime", "large population"],
    climate: {
      avgTemp: 17,
      rainyDays: 72,
      windyDays: 88,
      bestMonths: ["Nov", "Dec", "Jan", "Feb", "Mar"]
    },
    transport: {
      uberAvg: 35, // Moderate distance from city center
      publicTransport: ["Golden Arrow", "Taxis", "MyCiTi planned"],
      parking: "Good availability, mostly street parking"
    }
  },

   {
    name: "Khayelitsha",
    borough: "Cape Flats",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -34.0351, lng: 18.6919 },
    demographics: {
      population: 400000, // One of largest townships
      medianAge: 25,
      medianIncome: 95000,
      ethnicComposition: {
        white: 0.5, black: 96, coloured: 2.5, indian: 0.5, other: 0.5
      },
      educationLevel: {
        matric: 52, diploma: 12, degree: 4
      }
    },
    housing: {
      avgRent: 3500, // Informal settlements mixed with formal
      rentalRange: { min: 1500, max: 8000 },
      rentByBedroom: {
        studio: {
          min: 1500, max: 2500, avg: 2000,
          typical: "Backyard room or small informal structure",
          features: ["Basic shelter", "Shared facilities", "Community access", "Transport links"],
          description: "Basic accommodation, often shared facilities. Popular with job seekers and students.",
          availability: "High - 80+ available monthly",
          popularAreas: ["Site B", "Site C", "Harare"]
        },
        oneBed: {
          min: 2000, max: 4000, avg: 3000,
          typical: "RDP house room or small formal dwelling",
          features: ["Basic amenities", "Electricity", "Water access", "Community"],
          description: "Formal and informal options. Many RDP houses with additional rooms for rent.",
          availability: "Very High - 100+ available monthly",
          popularAreas: ["Ilitha Park", "Kuyasa", "Town Two"]
        },
        twoBed: {
          min: 3000, max: 6000, avg: 4500,
          typical: "RDP house or small formal house",
          features: ["Basic house", "Yard space", "Community facilities", "School access"],
          description: "Family accommodation, mix of RDP and self-built homes. Growing formal housing sector.",
          availability: "High - 60+ available monthly",
          popularAreas: ["Kuyasa", "Ilitha Park", "Greenpoint"]
        },
        threeBed: {
          min: 4000, max: 7000, avg: 5500,
          typical: "Larger RDP house or improved dwelling",
          features: ["Family space", "Multiple rooms", "Yard", "Community access"],
          description: "Larger family homes, often extended RDP houses. Popular with established families.",
          availability: "Moderate - 25+ available monthly",
          popularAreas: ["Ilitha Park", "Kuyasa", "Makhaza"]
        },
        fourBed: {
          min: 5500, max: 8000, avg: 6750,
          typical: "Large family house or multi-family dwelling",
          features: ["Large family space", "Multiple facilities", "Community hub", "Extended family"],
          description: "Rare but available for large families. Often houses multiple generations.",
          availability: "Limited - 8-12 available monthly",
          popularAreas: ["Ilitha Park", "Makhaza"]
        }
      },
      avgSalePrice: 320000,
      rentalAvailability: 35,
      homeOwnershipRate: 45, // Mix of informal settlements
      pricePerSqM: 4200,
      rentalYield: 12.5, // High yield due to low property values
      propertyTypes: {
        rdpHouses: 45, informal: 35, formal: 15, backyard: 5
      },
      utilities: {
        included: ["Basic electricity", "Community water"],
        excluded: ["Prepaid electricity top-ups", "Internet"],
        avgUtilityCost: 800
      }
    },
    safety: {
      crimeRate: 2.1, // Surprisingly moderate total crimes, high contact crimes
      safetyScore: 5.8,
      crimeTypes: {
        violent: 21, property: 12, drug: 8, other: 15 // High contact crimes (#7)
      }
    },
    amenities: {
      restaurants: 15,
      schools: 85, // Many primary schools
      parks: 8,
      transitScore: 72, // Major taxi hub
      walkabilityScore: 75,
      bikeScore: 35,
      groceryStores: 45,
      hospitals: 2,
      beaches: 0
    },
    description: "Large township with mix of formal housing and informal settlements, major economic hub for townships",
    tags: ["township", "informal settlements", "working-class", "taxi hub", "diverse housing"],
    climate: {
      avgTemp: 17,
      rainyDays: 70,
      windyDays: 85,
      bestMonths: ["Nov", "Dec", "Jan", "Feb", "Mar"]
    },
    transport: {
      uberAvg: 45, // Far from city center
      publicTransport: ["Golden Arrow", "Taxis", "BRT planned"],
      parking: "Street parking, generally available"
    }
  },

  {
    name: "Fish Hoek",
    borough: "Southern Peninsula",
    city: "Cape Town",
    state: "Western Cape",
    coordinates: { lat: -34.1333, lng: 18.4333 },
    demographics: {
      population: 11200,
      medianAge: 52,
      medianIncome: 385000,
      ethnicComposition: {
        white: 68, black: 8, coloured: 22, indian: 2, other: 0
      },
      educationLevel: {
        matric: 88, diploma: 42, degree: 48
      }
    },
    housing: {
      avgRent: 19500,
      rentalRange: { min: 10000, max: 40000 },
      rentByBedroom: {
        studio: { 
          min: 10000, max: 13000, avg: 11500,
          typical: "Retirement-friendly apartment with sea glimpses",
          features: ["Sea glimpses", "Retirement-friendly", "Safe area", "Mountain views"],
          description: "Limited options, mostly for retirees or young professionals. Safe and quiet area.",
          availability: "Very limited - 3-5 available monthly",
          popularAreas: ["Main Road", "Mountainside", "Central Fish Hoek"]
        },
        oneBed: { 
          min: 12000, max: 20000, avg: 16000,
          typical: "Apartment or flat with mountain or sea views",
          features: ["Mountain/sea views", "Quiet area", "Safe environment", "Retirement-friendly"],
          description: "Popular with retirees and young families. Many properties have been recently renovated.",
          availability: "Limited - 12-18 available monthly",
          popularAreas: ["Main Road", "Mountainside", "Simon's Town Road"]
        },
        twoBed: {
          min: 16000, max: 28000, avg: 22000,
          typical: "Family-friendly house or apartment with mountain or sea views",
          features: ["Mountain/sea views", "Family-friendly", "Quiet area", "Safe environment"],
          description: "Popular with families. Many properties have large gardens and are well-maintained.",
          availability: "Moderate - 20-25 available monthly",
          popularAreas: ["Main Road", "Mountainside", "Simon's Town Road"]
        },
        threeBed: {
          min: 22000, max: 35000, avg: 28500,
          typical: "Large family house with sea or mountain views and garden",
          features: ["Sea/mountain views", "Large garden", "Family-friendly", "Safe area"],
          description: "Spacious family homes, popular with established families and retirees. Many with large gardens.",
          availability: "Limited - 8-12 available monthly",
          popularAreas: ["Mountainside", "Central Fish Hoek", "Clovelly"]
        },
        fourBed: {
          min: 28000, max: 40000, avg: 34000,
          typical: "Large family home with exceptional views and premium location",
          features: ["Exceptional views", "Large property", "Premium location", "Multiple bathrooms"],
          description: "Premium family homes with exceptional views. Very limited availability in this quiet area.",
          availability: "Very limited - 3-5 available monthly",
          popularAreas: ["Mountainside", "Premium locations", "Clovelly"]
        }
      },
      avgSalePrice: 1800000,
      rentalAvailability: 18,
      homeOwnershipRate: 62,
      pricePerSqM: 18000,
      rentalYield: 6.2,
      propertyTypes: {
        houses: 60, apartments: 30, cottages: 10
      },
      utilities: {
        included: ["Water", "Refuse"],
        excluded: ["Electricity", "Internet"],
        avgUtilityCost: 1500
      }
    },
    safety: {
      crimeRate: 2.5,
      safetyScore: 7.5,
      crimeTypes: { violent: 4, property: 18, drug: 6, other: 12 },
      securityFeatures: ["Community policing", "Street lighting", "CCTV"]
    },
    education: {
      schools: {
        primary: ["Fish Hoek Primary", "Noordhoek Primary"],
        highSchool: ["Fish Hoek High", "Noordhoek High"]
        },
        educationScore: 8,
        schoolTypes: ["Public", "Private"],
        schoolRating: 7.5
    },
    amenities: {
      restaurants: 15,
      parks: 2,
      transitScore: 55,
      walkabilityScore: 85,
      bikeScore: 90,
      groceryStores: 10,
      hospitals: 1,
      beaches: 1,
      entertainment: ["Surfing", "Beach activities", "Pavilion"],
      shopping: ["Local shops", "Supermarkets", "Markets"]
      },
      demographics: {
      population: 11200,
      medianAge: 52,
      medianIncome: 385000,
      ethnicComposition: {
        white: 68, black: 8, coloured: 22, indian: 2, other: 0
      },
      educationLevel: {
        matric: 88, diploma: 42, degree: 48
      }
        },
        climate: {
          avgTemp: 18,
          rainyDays: 90,
          windyDays: 110,
          sunnyDays: 200,
          humidity: 60,
          bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb", "Mar"]
        },
        transport: {
          uberAvg: 40, // Distance from Cape Town city center
          publicTransport: ["Train line", "Limited bus service", "Taxis"],
          parking: "Generally good, busy in summer"
        }
      }
]

// Generate affordability categories based on 2025 Cape Town rental market data
function getAffordabilityCategory(avgRent) {
  if (avgRent >= 50000) return 'Ultra-Luxury'; // Constantia, premium Camps Bay
  if (avgRent >= 35000) return 'Luxury';        // High-end Atlantic Seaboard
  if (avgRent >= 20000) return 'Expensive';     // Mid-range Atlantic Seaboard, premium Southern Suburbs
  if (avgRent >= 12000) return 'Moderate';      // Standard Southern/Northern Suburbs
  if (avgRent >= 8000) return 'Affordable';     // Budget suburbs, Cape Flats formal housing
  return 'Budget';                               // Cape Flats, informal settlements
}

// Generate sample crime data for Cape Town suburbs
function generateCrimeData(neighborhood) {
  const crimeIncidents = [];

  // Crime type mappings
  const crimeTypeMap = {
    violent: {
      category: 'VIOLENT',
      types: ['ASSAULT', 'ROBBERY', 'DOMESTIC_VIOLENCE', 'HOMICIDE']
    },
    property: {
      category: 'PROPERTY',
      types: ['BURGLARY', 'THEFT', 'VEHICLE_THEFT', 'VANDALISM']
    },
    drug: {
      category: 'DRUG',
      types: ['DRUG_OFFENSE']
    },
    other: {
      category: 'OTHER',
      types: ['FRAUD', 'OTHER']
    }
  };

  const severityLevels = ['LOW', 'MEDIUM', 'HIGH'];
  const timesOfDay = ['MORNING', 'AFTERNOON', 'EVENING', 'NIGHT'];
  const daysOfWeek = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'];

  // Generate incidents based on crime rates
  const totalIncidents = Math.floor((neighborhood.safety.crimeRate * neighborhood.demographics.population) / 1000);

  for (let i = 0; i < totalIncidents; i++) {
    // Pick random crime type based on neighborhood's crime distribution
    const crimeTypeKeys = Object.keys(neighborhood.safety.crimeTypes);
    const randomTypeKey = crimeTypeKeys[Math.floor(Math.random() * crimeTypeKeys.length)];
    const crimeTypeInfo = crimeTypeMap[randomTypeKey];

    const randomDate = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);

    crimeIncidents.push({
      neighborhood: neighborhood.name,
      borough: neighborhood.borough,
      incidentType: crimeTypeInfo.types[Math.floor(Math.random() * crimeTypeInfo.types.length)],
      category: crimeTypeInfo.category,
      date: randomDate,
      coordinates: {
        lat: neighborhood.coordinates.lat + (Math.random() - 0.5) * 0.01,
        lng: neighborhood.coordinates.lng + (Math.random() - 0.5) * 0.01
      },
      severity: severityLevels[Math.floor(Math.random() * severityLevels.length)],
      timeOfDay: timesOfDay[Math.floor(Math.random() * timesOfDay.length)],
      dayOfWeek: daysOfWeek[Math.floor(Math.random() * daysOfWeek.length)],
      month: randomDate.getMonth() + 1,
      year: randomDate.getFullYear(),
      resolved: Math.random() > 0.7, // 30% resolved
      dataSource: 'Cape Town Crime Statistics 2024'
    });
  }

  return crimeIncidents;
}

async function importCapeTownData(options = {}) {
  try {
    logger.info('🇿🇦 Starting Cape Town data import...');

    // Only clear all data if explicitly requested
    if (options.clearAll) {
      logger.info('Clearing existing neighborhood data...');
      await Neighborhood.deleteMany({});
      await CrimeData.deleteMany({});
    } else {
      logger.info('Performing selective updates (preserving existing data)...');
    }

    let importedCount = 0;
    let updatedCount = 0;
    let crimeCount = 0;

    for (const suburbData of capeReggSuburbs) {
      try {
        // Add calculated fields
        const neighborhoodDoc = {
          ...suburbData,
          fullAddress: `${suburbData.name}, ${suburbData.borough}, ${suburbData.city}, ${suburbData.state}`,
          affordabilityCategory: getAffordabilityCategory(suburbData.housing.avgRent),
          lastUpdated: new Date(),
          dataSource: 'Cape Town Real Estate & Crime Data 2024'
        };

        // Generate vector embedding
        logger.info(`Generating embedding for ${suburbData.name}...`);
        const embedding = await geminiService.generateNeighborhoodEmbedding(neighborhoodDoc);
        neighborhoodDoc.vectorEmbedding = embedding;

        // Check if neighborhood already exists
        const existingNeighborhood = await Neighborhood.findOne({
          name: suburbData.name,
          borough: suburbData.borough
        });

        // Save neighborhood using upsert to handle duplicates
        const neighborhood = await Neighborhood.findOneAndUpdate(
          { name: suburbData.name, borough: suburbData.borough },
          neighborhoodDoc,
          { upsert: true, new: true, setDefaultsOnInsert: true }
        );

        if (existingNeighborhood) {
          updatedCount++;
          logger.info(`🔄 Updated ${suburbData.name}`);
        } else {
          importedCount++;
          logger.info(`✅ Created ${suburbData.name}`);
        }

        // Clear existing crime data for this neighborhood and generate new data
        await CrimeData.deleteMany({ neighborhoodId: neighborhood._id });

        const crimeIncidents = generateCrimeData(suburbData);
        for (const incident of crimeIncidents) {
          const crimeDoc = new CrimeData({
            ...incident,
            neighborhoodId: neighborhood._id,
            neighborhoodName: neighborhood.name
          });
          await crimeDoc.save();
          crimeCount++;
        }

      } catch (error) {
        logger.error(`❌ Error processing ${suburbData.name}:`, error);
      }
    }

    logger.info(`🎉 Cape Town data import completed!`);
    logger.info(`📊 Created ${importedCount} new suburbs, updated ${updatedCount} existing suburbs`);
    logger.info(`📊 Generated ${crimeCount} crime incidents`);
    
    // Display summary
    const summary = await Neighborhood.aggregate([
      {
        $group: {
          _id: '$borough',
          count: { $sum: 1 },
          avgRent: { $avg: '$housing.avgRent' },
          avgSafety: { $avg: '$safety.safetyScore' }
        }
      },
      { $sort: { avgRent: -1 } }
    ]);

    logger.info('📈 Summary by Borough:');
    summary.forEach(borough => {
      logger.info(`  ${borough._id}: ${borough.count} suburbs, R${Math.round(borough.avgRent).toLocaleString()} avg rent, ${borough.avgSafety.toFixed(1)} safety score`);
    });

  } catch (error) {
    logger.error('❌ Error during Cape Town data import:', error);
    throw error;
  }
}

// Function to update specific neighborhoods only
async function updateSpecificNeighborhoods(neighborhoodNames) {
  try {
    logger.info(`🎯 Updating specific neighborhoods: ${neighborhoodNames.join(', ')}`);

    const suburbsToUpdate = capeReggSuburbs.filter(suburb =>
      neighborhoodNames.includes(suburb.name)
    );

    if (suburbsToUpdate.length === 0) {
      logger.warn('No matching neighborhoods found to update');
      return;
    }

    let updatedCount = 0;
    let crimeCount = 0;

    for (const suburbData of suburbsToUpdate) {
      try {
        // Add calculated fields
        const neighborhoodDoc = {
          ...suburbData,
          fullAddress: `${suburbData.name}, ${suburbData.borough}, ${suburbData.city}, ${suburbData.state}`,
          affordabilityCategory: getAffordabilityCategory(suburbData.housing.avgRent),
          lastUpdated: new Date(),
          dataSource: 'Cape Town Real Estate & Crime Data 2024'
        };

        // Generate vector embedding
        logger.info(`Generating embedding for ${suburbData.name}...`);
        const embedding = await geminiService.generateNeighborhoodEmbedding(neighborhoodDoc);
        neighborhoodDoc.vectorEmbedding = embedding;

        // Update neighborhood
        const neighborhood = await Neighborhood.findOneAndUpdate(
          { name: suburbData.name, borough: suburbData.borough },
          neighborhoodDoc,
          { upsert: true, new: true, setDefaultsOnInsert: true }
        );
        updatedCount++;

        // Clear existing crime data for this neighborhood and generate new data
        await CrimeData.deleteMany({ neighborhoodId: neighborhood._id });

        const crimeIncidents = generateCrimeData(suburbData);
        for (const incident of crimeIncidents) {
          const crimeDoc = new CrimeData({
            ...incident,
            neighborhoodId: neighborhood._id,
            neighborhoodName: neighborhood.name
          });
          await crimeDoc.save();
          crimeCount++;
        }

        logger.info(`✅ Updated ${suburbData.name} with ${crimeIncidents.length} crime incidents`);

      } catch (error) {
        logger.error(`❌ Error updating ${suburbData.name}:`, error);
      }
    }

    logger.info(`🎉 Selective update completed!`);
    logger.info(`📊 Updated ${updatedCount} neighborhoods and ${crimeCount} crime incidents`);

  } catch (error) {
    logger.error('❌ Error during selective update:', error);
    throw error;
  }
}

module.exports = { importCapeTownData, updateSpecificNeighborhoods, capeReggSuburbs };

// Run import if called directly
if (require.main === module) {
  const { connectDB } = require('../src/config/database');

  connectDB().then(async () => {
    await importCapeTownData();
    process.exit(0);
  }).catch(error => {
    logger.error('Database connection failed:', error);
    process.exit(1);
  });
}
