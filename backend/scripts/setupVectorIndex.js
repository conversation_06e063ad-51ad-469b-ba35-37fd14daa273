const { MongoClient } = require('mongodb');
require('dotenv').config();

const { logger } = require('../src/utils/logger');

async function setupVectorIndex() {
  const client = new MongoClient(process.env.MONGODB_URI);
  
  try {
    await client.connect();
    logger.info('Connected to MongoDB');
    
    const db = client.db(process.env.MONGODB_DB_NAME || 'cityinsights');
    const collection = db.collection('neighborhoods');
    
    // Create vector search index
    const vectorIndexDefinition = {
      name: 'neighborhood_vector_index',
      definition: {
        fields: [
          {
            type: 'vector',
            path: 'vectorEmbedding',
            numDimensions: 768,
            similarity: 'cosine'
          }
        ]
      }
    };
    
    logger.info('Creating vector search index...');
    
    try {
      // Note: In a real MongoDB Atlas setup, you would create this index through the Atlas UI
      // or using the Atlas Admin API. This is a placeholder for the index creation.
      
      logger.info('Vector index configuration:');
      logger.info(JSON.stringify(vectorIndexDefinition, null, 2));
      
      logger.info(`
📋 MANUAL SETUP REQUIRED:

To complete the vector search setup, please follow these steps in MongoDB Atlas:

1. Go to your MongoDB Atlas cluster
2. Navigate to the "Search" tab
3. Click "Create Search Index"
4. Choose "JSON Editor"
5. Use the following index definition:

{
  "fields": [
    {
      "type": "vector",
      "path": "vectorEmbedding",
      "numDimensions": 768,
      "similarity": "cosine"
    }
  ]
}

6. Name the index: "neighborhood_vector_index"
7. Select the "neighborhoods" collection
8. Click "Create Search Index"

The index will take a few minutes to build. Once it's ready, vector search will be available.
      `);
      
    } catch (error) {
      logger.error('Error creating vector index:', error);
    }
    
    // Create regular indexes for performance
    logger.info('Creating regular database indexes...');
    
    const indexes = [
      { name: 1, borough: 1 }, // Compound index for name and borough
      { coordinates: '2dsphere' }, // Geospatial index
      { 'housing.avgRent': 1 }, // Housing price index
      { 'safety.safetyScore': -1 }, // Safety score index (descending)
      { 'amenities.transitScore': -1 }, // Transit score index (descending)
      { tags: 1 }, // Tags index
      { borough: 1 }, // Borough index
      { lastUpdated: -1 } // Last updated index
    ];
    
    for (const index of indexes) {
      try {
        await collection.createIndex(index);
        logger.info(`✓ Created index: ${JSON.stringify(index)}`);
      } catch (error) {
        if (error.code === 85) {
          logger.info(`Index already exists: ${JSON.stringify(index)}`);
        } else {
          logger.error(`Error creating index ${JSON.stringify(index)}:`, error);
        }
      }
    }
    
    // Create indexes for crime data collection
    const crimeCollection = db.collection('crimedatas');
    
    const crimeIndexes = [
      { neighborhood: 1, date: -1 }, // Neighborhood and date compound index
      { borough: 1, incidentType: 1, date: -1 }, // Borough, type, and date
      { coordinates: '2dsphere' }, // Geospatial index for crime locations
      { year: 1, month: 1 }, // Time-based index
      { category: 1, severity: 1 }, // Category and severity index
      { date: -1 } // Date index for time-based queries
    ];
    
    for (const index of crimeIndexes) {
      try {
        await crimeCollection.createIndex(index);
        logger.info(`✓ Created crime data index: ${JSON.stringify(index)}`);
      } catch (error) {
        if (error.code === 85) {
          logger.info(`Crime index already exists: ${JSON.stringify(index)}`);
        } else {
          logger.error(`Error creating crime index ${JSON.stringify(index)}:`, error);
        }
      }
    }
    
    logger.info('✅ Database indexes setup completed!');
    
  } catch (error) {
    logger.error('❌ Vector index setup failed:', error);
  } finally {
    await client.close();
  }
}

// Run the setup
if (require.main === module) {
  setupVectorIndex().then(() => {
    process.exit(0);
  });
}

module.exports = { setupVectorIndex };
