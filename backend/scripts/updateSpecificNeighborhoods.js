#!/usr/bin/env node

/**
 * <PERSON>ript to update specific neighborhoods without affecting others
 * Usage: node updateSpecificNeighborhoods.js "Khayelitsha" "Mitchells Plain"
 */

const { connectDB } = require('../src/config/database');
const { updateSpecificNeighborhoods } = require('./importCapeTownData');

async function main() {
  try {
    // Get neighborhood names from command line arguments
    const neighborhoodNames = process.argv.slice(2);
    
    if (neighborhoodNames.length === 0) {
      console.log('Usage: node updateSpecificNeighborhoods.js "Neighborhood1" "Neighborhood2" ...');
      console.log('Example: node updateSpecificNeighborhoods.js "Khayelitsha" "Mitchells Plain"');
      process.exit(1);
    }

    console.log(`🎯 Updating neighborhoods: ${neighborhoodNames.join(', ')}`);

    // Connect to database
    await connectDB();

    // Update specific neighborhoods
    await updateSpecificNeighborhoods(neighborhoodNames);

    console.log('✅ Update completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('❌ Update failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
