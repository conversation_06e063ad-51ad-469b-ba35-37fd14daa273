const { MongoClient } = require('mongodb');
const HouseRental = require('../src/models/HouseRental');
const { connectDB } = require('../src/config/database');
const logger = require('../src/utils/logger');
require('dotenv').config();

/**
 * <PERSON><PERSON>t to import house rental data from the houseRentals collection
 * and transform it into the HouseRental model format
 */

async function importHouseRentals() {
  let client;
  
  try {
    // Connect to MongoDB
    await connectDB();
    logger.info('Connected to MongoDB');

    // Connect to the raw MongoDB client for accessing the houseRentals database
    client = new MongoClient(process.env.MONGODB_URI);
    await client.connect();
    const houseRentalsDB = client.db('houseRentals');

    // Get the raw house rentals data from the properties collection
    const houseRentalsCollection = houseRentalsDB.collection('properties');
    const rawRentals = await houseRentalsCollection.find({}).toArray();
    
    logger.info(`Found ${rawRentals.length} rental properties to import`);

    if (rawRentals.length === 0) {
      logger.warn('No rental data found in houseRentals collection');
      return;
    }

    // Clear existing data
    await HouseRental.deleteMany({});
    logger.info('Cleared existing rental data');

    let imported = 0;
    let skipped = 0;

    for (const rental of rawRentals) {
      try {
        // Transform the raw data to match our schema
        const transformedRental = transformRentalData(rental);
        
        // Create new rental document
        const newRental = new HouseRental(transformedRental);
        await newRental.save();
        
        imported++;
        
        if (imported % 10 === 0) {
          logger.info(`Imported ${imported} properties...`);
        }
      } catch (error) {
        console.error(`Error importing rental ${rental._id}:`, error.message);
        skipped++;
      }
    }

    logger.info(`✅ Import completed: ${imported} imported, ${skipped} skipped`);

    // Generate some statistics
    const stats = await generateImportStats();
    logger.info('Import Statistics:', stats);

  } catch (error) {
    console.error('Error importing house rentals:', error);
    throw error;
  } finally {
    if (client) {
      await client.close();
    }
  }
}

/**
 * Transform raw rental data to match our schema
 */
function transformRentalData(rental) {
  // Extract bedroom count from title if not provided
  const extractBedrooms = (title) => {
    if (rental.bedrooms !== undefined) return rental.bedrooms;
    
    const match = title.match(/(\d+)\s*bedroom/i);
    if (match) return parseInt(match[1]);
    
    if (title.toLowerCase().includes('studio')) return 0;
    return 1; // Default
  };

  // Extract property type from title
  const extractPropertyType = (title) => {
    if (rental.propertyType) return rental.propertyType;
    
    const titleLower = title.toLowerCase();
    if (titleLower.includes('house')) return 'House';
    if (titleLower.includes('apartment')) return 'Apartment';
    if (titleLower.includes('penthouse')) return 'Penthouse';
    if (titleLower.includes('loft')) return 'Loft';
    if (titleLower.includes('studio')) return 'Studio';
    if (titleLower.includes('townhouse')) return 'Townhouse';
    return 'Apartment'; // Default
  };

  // Determine category based on price
  const getCategory = (price) => {
    if (rental.category) return rental.category;
    
    if (price >= 50000) return 'Ultra-Luxury';
    if (price >= 35000) return 'Luxury';
    if (price >= 20000) return 'Moderate';
    return 'Budget';
  };

  // Determine size based on bedrooms and floor size
  const getSize = (bedrooms, floorSize) => {
    if (rental.size) return rental.size;
    
    if (floorSize) {
      if (floorSize >= 150) return 'Extra Large';
      if (floorSize >= 100) return 'Large';
      if (floorSize >= 60) return 'Medium';
      return 'Compact';
    }
    
    // Based on bedrooms
    if (bedrooms >= 4) return 'Extra Large';
    if (bedrooms >= 3) return 'Large';
    if (bedrooms >= 2) return 'Medium';
    return 'Compact';
  };

  // Get coordinates for the location (you might want to add a geocoding service)
  const getCoordinates = (location) => {
    // Basic mapping for major Cape Town areas
    const locationCoords = {
      'Cape Town City Centre': { lat: -33.9249, lng: 18.4241 },
      'Sea Point': { lat: -33.9248, lng: 18.3917 },
      'Camps Bay': { lat: -33.9489, lng: 18.3758 },
      'Constantia': { lat: -34.0235, lng: 18.4198 },
      'Claremont': { lat: -33.9847, lng: 18.4647 },
      'Woodstock': { lat: -33.9258, lng: 18.4467 },
      'Observatory': { lat: -33.9333, lng: 18.4667 },
      'Gardens': { lat: -33.9333, lng: 18.4167 },
      'Kenilworth': { lat: -33.9667, lng: 18.4833 },
      'Muizenberg': { lat: -34.1047, lng: 18.4669 },
      'Stellenbosch': { lat: -33.9321, lng: 18.8602 },
      'Paarl': { lat: -33.7369, lng: 18.9584 },
      'Somerset West': { lat: -34.0781, lng: 18.8419 },
      'Milnerton': { lat: -33.8569, lng: 18.4956 },
      'Wynberg': { lat: -34.0047, lng: 18.4647 },
      'Foreshore': { lat: -33.9167, lng: 18.4167 },
      'Bantry Bay': { lat: -33.9167, lng: 18.3833 },
      'Fresnaye': { lat: -33.9167, lng: 18.3833 },
      'Bo Kaap': { lat: -33.9167, lng: 18.4167 },
      'De Waterkant': { lat: -33.9167, lng: 18.4167 },
      'Higgovale': { lat: -33.9333, lng: 18.4167 },
      'Mouille Point': { lat: -33.9167, lng: 18.4167 },
      'Diep River': { lat: -34.0333, lng: 18.4833 },
      'Belhar': { lat: -33.8833, lng: 18.6167 },
      'Claremont Upper': { lat: -33.9847, lng: 18.4647 }
    };

    return locationCoords[location] || { lat: -33.9249, lng: 18.4241 }; // Default to Cape Town center
  };

  const bedrooms = extractBedrooms(rental.title || '');
  const propertyType = extractPropertyType(rental.title || '');
  const category = getCategory(rental.price);
  const size = getSize(bedrooms, rental.floorSize);
  const coordinates = getCoordinates(rental.location);

  return {
    title: rental.title || `${bedrooms} Bedroom ${propertyType}`,
    location: rental.location,
    price: rental.price,
    bedrooms: bedrooms,
    bathrooms: rental.bathrooms || Math.max(1, Math.floor(bedrooms * 0.75)),
    floorSize: rental.floorSize || null,
    parking: rental.parking || 0,
    propertyType: propertyType,
    category: category,
    size: size,
    features: rental.features || [],
    amenities: rental.amenities || [],
    description: rental.description || `Beautiful ${bedrooms} bedroom ${propertyType.toLowerCase()} in ${rental.location}`,
    coordinates: coordinates,
    availability: {
      available: true,
      availableFrom: new Date(),
      leaseTerm: '12 months'
    },
    furnished: rental.furnished || 'Unfurnished',
    lastUpdated: new Date(),
    dataSource: 'Cape Town Rental Market 2025'
  };
}

/**
 * Generate import statistics
 */
async function generateImportStats() {
  const [
    totalCount,
    locationStats,
    priceStats,
    bedroomStats
  ] = await Promise.all([
    HouseRental.countDocuments(),
    HouseRental.aggregate([
      { $group: { _id: '$location', count: { $sum: 1 }, avgPrice: { $avg: '$price' } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]),
    HouseRental.aggregate([
      { $group: { _id: null, avgPrice: { $avg: '$price' }, minPrice: { $min: '$price' }, maxPrice: { $max: '$price' } } }
    ]),
    HouseRental.aggregate([
      { $group: { _id: '$bedrooms', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ])
  ]);

  return {
    totalProperties: totalCount,
    topLocations: locationStats.slice(0, 5),
    priceRange: priceStats[0] || {},
    bedroomDistribution: bedroomStats
  };
}

// Run the import if this script is executed directly
if (require.main === module) {
  importHouseRentals()
    .then(() => {
      logger.info('House rental import completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('House rental import failed:', error);
      process.exit(1);
    });
}

module.exports = { importHouseRentals, transformRentalData };
