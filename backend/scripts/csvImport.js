const fs = require('fs');
const csv = require('csv-parser');
const mongoose = require('mongoose');
require('dotenv').config();

const Neighborhood = require('../src/models/Neighborhood');
const { logger } = require('../src/utils/logger');

// Sample CSV data for neighborhoods
const sampleCSVData = `name,borough,lat,lng,population,median_age,median_income,avg_rent,safety_score,transit_score,walkability_score,restaurants,schools,parks,description,tags
SoHo,Manhattan,40.7230,-74.0030,15000,35,120000,5500,8.5,95,98,150,8,3,"Trendy neighborhood known for cast-iron architecture and shopping","trendy,expensive,artistic,shopping"
Greenwich Village,Manhattan,40.7335,-74.0027,22000,38,110000,4800,9.0,92,96,200,12,5,"Historic bohemian neighborhood with tree-lined streets","historic,bohemian,cultural,walkable"
Williamsburg,Brooklyn,40.7081,-73.9571,35000,32,85000,3800,7.5,85,90,180,15,8,"Hip neighborhood with artisanal everything and waterfront views","hip,young professionals,artisanal,waterfront"
Park Slope,Brooklyn,40.6782,-73.9776,28000,40,95000,4200,9.2,88,94,120,20,12,"Family-friendly neighborhood with excellent schools","family-friendly,schools,parks,safe"
Astoria,Queens,40.7698,-73.9442,45000,35,65000,2800,7.0,82,85,250,18,10,"Diverse neighborhood known for authentic international cuisine","diverse,affordable,international food,multicultural"
Upper East Side,Manhattan,40.7736,-73.9566,55000,42,130000,5200,9.5,90,92,180,25,8,"Upscale neighborhood with museums and Central Park access","upscale,museums,luxury,safe"
DUMBO,Brooklyn,40.7033,-73.9903,5000,33,140000,6000,8.8,88,95,80,5,4,"Trendy waterfront neighborhood with Manhattan views","trendy,waterfront,expensive,tech"
Long Island City,Queens,40.7505,-73.9342,20000,31,75000,3200,7.8,95,85,120,10,6,"Rapidly developing area with easy Manhattan access","developing,transit,affordable,young professionals"
Tribeca,Manhattan,40.7195,-74.0089,12000,40,180000,7500,9.3,85,90,100,6,2,"Exclusive neighborhood popular with celebrities","exclusive,luxury,family-friendly,expensive"
Red Hook,Brooklyn,40.6743,-74.0113,11000,35,55000,2900,6.5,65,75,60,8,5,"Artistic waterfront community with industrial charm","artistic,waterfront,affordable,industrial"
Chelsea,Manhattan,40.7465,-74.0014,25000,36,95000,4500,8.2,92,94,180,12,7,"Trendy neighborhood with galleries and nightlife","trendy,galleries,nightlife,LGBTQ-friendly"
Bushwick,Brooklyn,40.6942,-73.9442,140000,29,45000,2400,6.8,78,82,200,15,8,"Rapidly gentrifying area known for street art","gentrifying,street art,affordable,young professionals"
Forest Hills,Queens,40.7214,-73.8448,85000,45,75000,2600,8.5,85,88,100,20,15,"Quiet residential area with good schools","quiet,residential,family-friendly,schools"
Harlem,Manhattan,40.8116,-73.9465,120000,35,50000,2200,7.2,88,85,150,25,12,"Historic neighborhood with rich cultural heritage","historic,cultural,affordable,jazz"
Bay Ridge,Brooklyn,40.6264,-74.0299,65000,42,68000,2100,8.8,75,90,120,18,10,"Family-oriented neighborhood with Norwegian heritage","family-oriented,safe,affordable,Norwegian"`;

async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    logger.info('Connected to MongoDB');
  } catch (error) {
    logger.error('MongoDB connection error:', error);
    throw error;
  }
}

function parseCSVRow(row) {
  return {
    name: row.name?.trim(),
    borough: row.borough?.trim(),
    coordinates: {
      lat: parseFloat(row.lat),
      lng: parseFloat(row.lng)
    },
    demographics: {
      population: parseInt(row.population) || 0,
      medianAge: parseInt(row.median_age) || 35,
      medianIncome: parseInt(row.median_income) || 50000,
      educationLevel: {
        highSchool: Math.floor(Math.random() * 30) + 70,
        bachelors: Math.floor(Math.random() * 40) + 30,
        graduate: Math.floor(Math.random() * 30) + 15
      },
      ethnicComposition: {
        white: Math.floor(Math.random() * 60) + 20,
        black: Math.floor(Math.random() * 40) + 10,
        hispanic: Math.floor(Math.random() * 40) + 10,
        asian: Math.floor(Math.random() * 30) + 5,
        other: Math.floor(Math.random() * 10) + 2
      }
    },
    housing: {
      avgRent: parseInt(row.avg_rent) || 3000,
      avgSalePrice: (parseInt(row.avg_rent) || 3000) * 200 + Math.floor(Math.random() * 500000),
      rentalAvailability: Math.floor(Math.random() * 40) + 10,
      homeOwnershipRate: Math.floor(Math.random() * 60) + 20,
      pricePerSqFt: Math.floor((parseInt(row.avg_rent) || 3000) / 2) + Math.floor(Math.random() * 500)
    },
    safety: {
      crimeRate: Math.max(0.5, 10 - (parseFloat(row.safety_score) || 7)),
      safetyScore: parseFloat(row.safety_score) || 7,
      crimeTypes: {
        violent: Math.floor(Math.random() * 10),
        property: Math.floor(Math.random() * 20) + 10,
        drug: Math.floor(Math.random() * 5),
        other: Math.floor(Math.random() * 8)
      }
    },
    amenities: {
      restaurants: parseInt(row.restaurants) || Math.floor(Math.random() * 200) + 50,
      schools: parseInt(row.schools) || Math.floor(Math.random() * 20) + 5,
      parks: parseInt(row.parks) || Math.floor(Math.random() * 15) + 2,
      transitScore: parseInt(row.transit_score) || Math.floor(Math.random() * 40) + 60,
      walkabilityScore: parseInt(row.walkability_score) || Math.floor(Math.random() * 50) + 50,
      bikeScore: Math.floor(Math.random() * 50) + 40,
      groceryStores: Math.floor(Math.random() * 30) + 10,
      hospitals: Math.floor(Math.random() * 5) + 1
    },
    description: row.description || `${row.name} is a neighborhood in ${row.borough}.`,
    tags: row.tags ? row.tags.split(',').map(tag => tag.trim()) : [],
    lastUpdated: new Date(),
    dataSource: 'CSV Import'
  };
}

async function importFromCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => {
        try {
          const neighborhood = parseCSVRow(data);
          if (neighborhood.name && neighborhood.borough) {
            results.push(neighborhood);
          }
        } catch (error) {
          logger.warn(`Skipping invalid row: ${error.message}`);
        }
      })
      .on('end', () => {
        logger.info(`📄 Parsed ${results.length} neighborhoods from CSV`);
        resolve(results);
      })
      .on('error', reject);
  });
}

async function createSampleCSV() {
  const csvPath = './data/sample_neighborhoods.csv';
  
  // Create data directory if it doesn't exist
  if (!fs.existsSync('./data')) {
    fs.mkdirSync('./data', { recursive: true });
  }
  
  fs.writeFileSync(csvPath, sampleCSVData);
  logger.info(`📄 Created sample CSV file: ${csvPath}`);
  return csvPath;
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';
  
  try {
    await connectDB();
    
    switch (command) {
      case 'import':
        const csvFile = args[1];
        if (!csvFile) {
          logger.error('Please provide CSV file path');
          process.exit(1);
        }
        
        if (!fs.existsSync(csvFile)) {
          logger.error(`CSV file not found: ${csvFile}`);
          process.exit(1);
        }
        
        logger.info(`📥 Importing from CSV: ${csvFile}`);
        
        // Clear existing data
        await Neighborhood.deleteMany({});
        logger.info('Cleared existing neighborhood data');
        
        // Import from CSV
        const neighborhoods = await importFromCSV(csvFile);
        const saved = await Neighborhood.insertMany(neighborhoods);
        
        logger.info(`✅ Successfully imported ${saved.length} neighborhoods`);
        break;
        
      case 'sample':
        logger.info('📄 Creating and importing sample data...');
        
        // Create sample CSV
        const samplePath = await createSampleCSV();
        
        // Clear existing data
        await Neighborhood.deleteMany({});
        logger.info('Cleared existing neighborhood data');
        
        // Import sample data
        const sampleNeighborhoods = await importFromCSV(samplePath);
        const savedSample = await Neighborhood.insertMany(sampleNeighborhoods);
        
        logger.info(`✅ Successfully imported ${savedSample.length} sample neighborhoods`);
        break;
        
      case 'create-csv':
        const outputPath = await createSampleCSV();
        logger.info(`✅ Sample CSV created at: ${outputPath}`);
        break;
        
      default:
        console.log(`
📊 CSV Import Tool for City Insights AI

Usage: node csvImport.js <command> [options]

Commands:
  import <file>  - Import neighborhoods from CSV file
  sample         - Create and import sample data
  create-csv     - Create sample CSV file only
  help           - Show this help message

Examples:
  node csvImport.js sample
  node csvImport.js import ./data/neighborhoods.csv
  node csvImport.js create-csv

CSV Format:
  Required columns: name, borough, lat, lng
  Optional columns: population, median_age, median_income, avg_rent, 
                   safety_score, transit_score, walkability_score,
                   restaurants, schools, parks, description, tags

Environment variables required:
  MONGODB_URI - MongoDB connection string
        `);
        break;
    }
  } catch (error) {
    logger.error('CSV import failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Export for use as module
module.exports = { importFromCSV, parseCSVRow, createSampleCSV };

// Run if called directly
if (require.main === module) {
  main();
}
