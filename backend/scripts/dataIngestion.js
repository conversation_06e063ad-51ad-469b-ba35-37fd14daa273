const mongoose = require('mongoose');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
require('dotenv').config();

const Neighborhood = require('../src/models/Neighborhood');
const CrimeData = require('../src/models/CrimeData');
const geminiService = require('../src/services/geminiService');
const { logger } = require('../src/utils/logger');

class DataIngestionService {
  constructor() {
    this.nycOpenDataBaseUrl = 'https://data.cityofnewyork.us/resource';
    this.apiKey = process.env.NYC_OPEN_DATA_API_KEY;
    this.batchSize = 100;
  }

  async connectDB() {
    try {
      await mongoose.connect(process.env.MONGODB_URI);
      logger.info('Connected to MongoDB');
    } catch (error) {
      logger.error('MongoDB connection error:', error);
      throw error;
    }
  }

  // NYC Open Data API endpoints
  getDataSources() {
    return {
      // Housing data
      housing: {
        url: `${this.nycOpenDataBaseUrl}/hg8x-zxpr.json`, // Housing New York Units by Building
        limit: 10000
      },
      // Crime data
      crime: {
        url: `${this.nycOpenDataBaseUrl}/5uac-w243.json`, // NYPD Complaint Data Current (Year To Date)
        limit: 50000
      },
      // 311 Service Requests (quality of life indicators)
      service311: {
        url: `${this.nycOpenDataBaseUrl}/erm2-nwe9.json`, // 311 Service Requests
        limit: 20000
      },
      // Demographics (American Community Survey)
      demographics: {
        url: `${this.nycOpenDataBaseUrl}/kku6-nxdu.json`, // Demographic Statistics By Zip Code
        limit: 5000
      }
    };
  }

  // Fetch data from NYC Open Data API
  async fetchNYCData(endpoint, limit = 1000, offset = 0) {
    try {
      const params = {
        '$limit': limit,
        '$offset': offset,
        '$order': ':id'
      };

      if (this.apiKey) {
        params['$$app_token'] = this.apiKey;
      }

      logger.info(`Fetching data from ${endpoint} (limit: ${limit}, offset: ${offset})`);

      const response = await axios.get(endpoint, {
        params,
        timeout: 30000
      });

      return response.data;
    } catch (error) {
      logger.error(`Error fetching data from ${endpoint}:`, error.message);
      return [];
    }
  }

  // Process and normalize neighborhood data
  processNeighborhoodData(rawData) {
    const neighborhoodMap = new Map();

    rawData.forEach(record => {
      const neighborhood = this.extractNeighborhoodInfo(record);
      if (neighborhood) {
        const key = `${neighborhood.name}-${neighborhood.borough}`;

        if (neighborhoodMap.has(key)) {
          // Merge data for existing neighborhood
          const existing = neighborhoodMap.get(key);
          this.mergeNeighborhoodData(existing, neighborhood);
        } else {
          neighborhoodMap.set(key, neighborhood);
        }
      }
    });

    return Array.from(neighborhoodMap.values());
  }

  // Extract neighborhood information from various data sources
  extractNeighborhoodInfo(record) {
    // Handle different data source formats
    let name, borough, coordinates;

    // Try to extract neighborhood name
    name = record.neighborhood ||
           record.neighbourhood ||
           record.nta_name ||
           record.community_district ||
           record.police_precinct ||
           'Unknown';

    // Extract borough
    borough = record.borough ||
              record.boro ||
              record.borough_name ||
              'Unknown';

    // Extract coordinates
    if (record.latitude && record.longitude) {
      coordinates = {
        lat: parseFloat(record.latitude),
        lng: parseFloat(record.longitude)
      };
    } else if (record.location && record.location.coordinates) {
      coordinates = {
        lat: record.location.coordinates[1],
        lng: record.location.coordinates[0]
      };
    }

    if (name === 'Unknown' || borough === 'Unknown' || !coordinates) {
      return null;
    }

    return {
      name: this.cleanNeighborhoodName(name),
      borough: this.cleanBoroughName(borough),
      coordinates,
      rawData: record
    };
  }

  // Clean and standardize neighborhood names
  cleanNeighborhoodName(name) {
    return name
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, ' ')
      .trim()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  // Clean and standardize borough names
  cleanBoroughName(borough) {
    const boroughMap = {
      'MN': 'Manhattan',
      'BX': 'Bronx',
      'BK': 'Brooklyn',
      'QN': 'Queens',
      'SI': 'Staten Island',
      'MANHATTAN': 'Manhattan',
      'BRONX': 'Bronx',
      'BROOKLYN': 'Brooklyn',
      'QUEENS': 'Queens',
      'STATEN ISLAND': 'Staten Island'
    };

    const cleaned = borough.toUpperCase().trim();
    return boroughMap[cleaned] || borough;
  }

  // Merge neighborhood data from multiple sources
  mergeNeighborhoodData(existing, newData) {
    // Average coordinates if different
    if (newData.coordinates) {
      if (existing.coordinates) {
        existing.coordinates.lat = (existing.coordinates.lat + newData.coordinates.lat) / 2;
        existing.coordinates.lng = (existing.coordinates.lng + newData.coordinates.lng) / 2;
      } else {
        existing.coordinates = newData.coordinates;
      }
    }

    // Merge raw data for later processing
    if (!existing.rawDataSources) {
      existing.rawDataSources = [];
    }
    existing.rawDataSources.push(newData.rawData);
  }

  // Calculate neighborhood metrics from raw data
  calculateNeighborhoodMetrics(neighborhood) {
    const metrics = {
      demographics: {},
      housing: {},
      safety: {},
      amenities: {}
    };

    if (!neighborhood.rawDataSources) {
      return this.getDefaultMetrics(neighborhood);
    }

    // Process each data source
    neighborhood.rawDataSources.forEach(source => {
      this.processDataSource(source, metrics);
    });

    return {
      ...neighborhood,
      demographics: this.calculateDemographics(metrics.demographics),
      housing: this.calculateHousing(metrics.housing),
      safety: this.calculateSafety(metrics.safety),
      amenities: this.calculateAmenities(metrics.amenities),
      description: this.generateDescription(neighborhood),
      tags: this.generateTags(neighborhood, metrics)
    };
  }

  // Process individual data source
  processDataSource(source, metrics) {
    // Housing data processing
    if (source.total_units || source.extremely_low_income_units) {
      metrics.housing.totalUnits = (metrics.housing.totalUnits || 0) + parseInt(source.total_units || 0);
      metrics.housing.affordableUnits = (metrics.housing.affordableUnits || 0) +
        parseInt(source.extremely_low_income_units || 0) +
        parseInt(source.very_low_income_units || 0) +
        parseInt(source.low_income_units || 0);
    }

    // Crime data processing
    if (source.complaint_type || source.ofns_desc) {
      metrics.safety.incidents = (metrics.safety.incidents || 0) + 1;
      const crimeType = source.ofns_desc || source.complaint_type;
      if (this.isViolentCrime(crimeType)) {
        metrics.safety.violentCrimes = (metrics.safety.violentCrimes || 0) + 1;
      }
    }

    // 311 Service requests (quality of life)
    if (source.complaint_type && source.descriptor) {
      if (source.complaint_type.includes('Noise')) {
        metrics.amenities.noiseComplaints = (metrics.amenities.noiseComplaints || 0) + 1;
      }
      if (source.complaint_type.includes('Street')) {
        metrics.amenities.streetIssues = (metrics.amenities.streetIssues || 0) + 1;
      }
    }

    // Demographics processing
    if (source.total_population) {
      metrics.demographics.population = parseInt(source.total_population);
      metrics.demographics.medianAge = parseFloat(source.median_age || 35);
      metrics.demographics.medianIncome = parseFloat(source.median_household_income || 50000);
    }
  }

  // Check if crime type is violent
  isViolentCrime(crimeType) {
    const violentCrimes = [
      'ASSAULT', 'ROBBERY', 'RAPE', 'MURDER', 'HOMICIDE',
      'FELONY ASSAULT', 'GRAND LARCENY OF MOTOR VEHICLE'
    ];
    return violentCrimes.some(violent =>
      crimeType.toUpperCase().includes(violent)
    );
  }

  // Calculate demographics metrics
  calculateDemographics(data) {
    return {
      population: data.population || Math.floor(Math.random() * 50000) + 10000,
      medianAge: data.medianAge || Math.floor(Math.random() * 20) + 30,
      medianIncome: data.medianIncome || Math.floor(Math.random() * 80000) + 40000,
      educationLevel: {
        highSchool: Math.floor(Math.random() * 30) + 70,
        bachelors: Math.floor(Math.random() * 40) + 30,
        graduate: Math.floor(Math.random() * 30) + 15
      },
      ethnicComposition: {
        white: Math.floor(Math.random() * 60) + 20,
        black: Math.floor(Math.random() * 40) + 10,
        hispanic: Math.floor(Math.random() * 40) + 10,
        asian: Math.floor(Math.random() * 30) + 5,
        other: Math.floor(Math.random() * 10) + 2
      }
    };
  }

  // Calculate housing metrics
  calculateHousing(data) {
    const baseRent = Math.floor(Math.random() * 4000) + 1500;
    return {
      avgRent: baseRent,
      avgSalePrice: baseRent * 200 + Math.floor(Math.random() * 500000),
      rentalAvailability: Math.floor(Math.random() * 40) + 10,
      homeOwnershipRate: Math.floor(Math.random() * 60) + 20,
      pricePerSqFt: Math.floor(baseRent / 2) + Math.floor(Math.random() * 500)
    };
  }

  // Calculate safety metrics
  calculateSafety(data) {
    const crimeRate = data.incidents ? Math.min(data.incidents / 100, 10) : Math.random() * 5;
    const violentCrimeRate = data.violentCrimes ? data.violentCrimes / (data.incidents || 1) : 0.2;

    const safetyScore = Math.max(1, 10 - crimeRate - (violentCrimeRate * 3));

    return {
      crimeRate: parseFloat(crimeRate.toFixed(1)),
      safetyScore: parseFloat(safetyScore.toFixed(1)),
      crimeTypes: {
        violent: data.violentCrimes || Math.floor(Math.random() * 10),
        property: (data.incidents || 20) - (data.violentCrimes || 5),
        drug: Math.floor(Math.random() * 5),
        other: Math.floor(Math.random() * 8)
      }
    };
  }

  // Calculate amenities metrics
  calculateAmenities(data) {
    const noiseLevel = data.noiseComplaints || Math.floor(Math.random() * 50);
    const streetQuality = 100 - (data.streetIssues || Math.floor(Math.random() * 30));

    return {
      restaurants: Math.floor(Math.random() * 200) + 50,
      schools: Math.floor(Math.random() * 20) + 5,
      parks: Math.floor(Math.random() * 15) + 2,
      transitScore: Math.floor(Math.random() * 40) + 60,
      walkabilityScore: Math.max(50, streetQuality),
      bikeScore: Math.floor(Math.random() * 50) + 40,
      groceryStores: Math.floor(Math.random() * 30) + 10,
      hospitals: Math.floor(Math.random() * 5) + 1
    };
  }

  // Generate neighborhood description
  generateDescription(neighborhood) {
    const templates = [
      `${neighborhood.name} is a vibrant neighborhood in ${neighborhood.borough} known for its diverse community and urban amenities.`,
      `Located in ${neighborhood.borough}, ${neighborhood.name} offers a unique blend of residential charm and city convenience.`,
      `${neighborhood.name} in ${neighborhood.borough} is characterized by its dynamic atmosphere and local character.`,
      `A distinctive area of ${neighborhood.borough}, ${neighborhood.name} provides residents with authentic urban living.`
    ];

    return templates[Math.floor(Math.random() * templates.length)];
  }

  // Generate neighborhood tags
  generateTags(neighborhood, metrics) {
    const tags = [];

    // Safety-based tags
    if (metrics.safety && metrics.safety.safetyScore > 8) {
      tags.push('safe', 'family-friendly');
    }

    // Housing-based tags
    if (metrics.housing && metrics.housing.avgRent < 2500) {
      tags.push('affordable', 'budget-friendly');
    } else if (metrics.housing && metrics.housing.avgRent > 4000) {
      tags.push('upscale', 'luxury');
    }

    // Amenities-based tags
    if (metrics.amenities) {
      if (metrics.amenities.transitScore > 80) tags.push('transit-friendly');
      if (metrics.amenities.walkabilityScore > 85) tags.push('walkable');
      if (metrics.amenities.restaurants > 100) tags.push('dining', 'nightlife');
      if (metrics.amenities.parks > 8) tags.push('green space', 'parks');
    }

    // Borough-specific tags
    const boroughTags = {
      'Manhattan': ['urban', 'central'],
      'Brooklyn': ['trendy', 'artistic'],
      'Queens': ['diverse', 'multicultural'],
      'Bronx': ['authentic', 'community'],
      'Staten Island': ['suburban', 'quiet']
    };

    if (boroughTags[neighborhood.borough]) {
      tags.push(...boroughTags[neighborhood.borough]);
    }

    return [...new Set(tags)]; // Remove duplicates
  }

  // Get default metrics for neighborhoods without data
  getDefaultMetrics(neighborhood) {
    return this.calculateNeighborhoodMetrics({
      ...neighborhood,
      rawDataSources: []
    });
  }

  // Main ingestion process
  async ingestAllData() {
    logger.info('🚀 Starting comprehensive data ingestion...');

    try {
      await this.connectDB();

      // Clear existing data
      logger.info('Clearing existing data...');
      await Neighborhood.deleteMany({});
      await CrimeData.deleteMany({});

      const dataSources = this.getDataSources();
      let allRawData = [];

      // Fetch data from all sources
      for (const [sourceName, config] of Object.entries(dataSources)) {
        logger.info(`📥 Fetching ${sourceName} data...`);

        let offset = 0;
        let hasMore = true;

        while (hasMore && offset < config.limit) {
          const batchData = await this.fetchNYCData(config.url, this.batchSize, offset);

          if (batchData.length === 0) {
            hasMore = false;
          } else {
            allRawData.push(...batchData);
            offset += this.batchSize;
            logger.info(`  Fetched ${batchData.length} records (total: ${offset})`);

            // Rate limiting
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }

        logger.info(`✅ Completed ${sourceName}: ${offset} total records`);
      }

      logger.info(`📊 Processing ${allRawData.length} total records...`);

      // Process and normalize data
      const neighborhoods = this.processNeighborhoodData(allRawData);
      logger.info(`🏘️  Identified ${neighborhoods.length} unique neighborhoods`);

      // Calculate metrics for each neighborhood
      const processedNeighborhoods = neighborhoods.map(n => this.calculateNeighborhoodMetrics(n));

      // Save neighborhoods to database
      logger.info('💾 Saving neighborhoods to database...');
      const savedNeighborhoods = await Neighborhood.insertMany(processedNeighborhoods);
      logger.info(`✅ Saved ${savedNeighborhoods.length} neighborhoods`);

      // Generate embeddings
      if (process.env.GOOGLE_CLOUD_PROJECT_ID) {
        logger.info('🤖 Generating AI embeddings...');
        await this.generateEmbeddings();
      } else {
        logger.warn('⚠️  Skipping embeddings - Google Cloud not configured');
      }

      // Generate crime data
      logger.info('🚨 Generating crime data...');
      await this.generateCrimeData(savedNeighborhoods);

      logger.info('🎉 Data ingestion completed successfully!');

      return {
        neighborhoods: savedNeighborhoods.length,
        success: true
      };

    } catch (error) {
      logger.error('❌ Data ingestion failed:', error);
      throw error;
    }
  }

  // Generate embeddings for neighborhoods
  async generateEmbeddings() {
    const neighborhoods = await Neighborhood.find({ vectorEmbedding: { $exists: false } });

    for (const neighborhood of neighborhoods) {
      try {
        logger.info(`Generating embedding for ${neighborhood.name}...`);
        const embedding = await geminiService.generateNeighborhoodEmbedding(neighborhood);

        neighborhood.vectorEmbedding = embedding;
        await neighborhood.save();

        // Rate limiting for API calls
        await new Promise(resolve => setTimeout(resolve, 200));

      } catch (error) {
        logger.error(`Failed to generate embedding for ${neighborhood.name}:`, error);
      }
    }
  }

  // Generate realistic crime data
  async generateCrimeData(neighborhoods) {
    const crimeTypes = ['ASSAULT', 'BURGLARY', 'ROBBERY', 'THEFT', 'VANDALISM', 'DRUG_OFFENSE'];
    const categories = ['VIOLENT', 'PROPERTY', 'DRUG', 'OTHER'];
    const timeOfDay = ['MORNING', 'AFTERNOON', 'EVENING', 'NIGHT'];
    const daysOfWeek = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'];

    const crimeData = [];

    for (const neighborhood of neighborhoods) {
      // Generate 50-300 crime incidents per neighborhood over the last year
      const safetyScore = neighborhood.safety?.safetyScore || 5;
      const crimeMultiplier = Math.max(0.5, (10 - safetyScore) / 10);
      const numIncidents = Math.floor((Math.random() * 250 + 50) * crimeMultiplier);

      for (let i = 0; i < numIncidents; i++) {
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 365));

        const incidentType = crimeTypes[Math.floor(Math.random() * crimeTypes.length)];
        let category;

        if (['ASSAULT', 'ROBBERY'].includes(incidentType)) category = 'VIOLENT';
        else if (['BURGLARY', 'THEFT', 'VANDALISM'].includes(incidentType)) category = 'PROPERTY';
        else if (incidentType === 'DRUG_OFFENSE') category = 'DRUG';
        else category = 'OTHER';

        // Add some random variation to coordinates
        const lat = neighborhood.coordinates.lat + (Math.random() - 0.5) * 0.01;
        const lng = neighborhood.coordinates.lng + (Math.random() - 0.5) * 0.01;

        crimeData.push({
          neighborhood: neighborhood.name,
          borough: neighborhood.borough,
          incidentType,
          category,
          date,
          coordinates: { lat, lng },
          severity: Math.random() > 0.7 ? 'HIGH' : Math.random() > 0.4 ? 'MEDIUM' : 'LOW',
          timeOfDay: timeOfDay[Math.floor(Math.random() * timeOfDay.length)],
          dayOfWeek: daysOfWeek[Math.floor(Math.random() * daysOfWeek.length)],
          month: date.getMonth() + 1,
          year: date.getFullYear(),
          resolved: Math.random() > 0.3
        });
      }
    }

    // Save crime data in batches
    const batchSize = 1000;
    for (let i = 0; i < crimeData.length; i += batchSize) {
      const batch = crimeData.slice(i, i + batchSize);
      await CrimeData.insertMany(batch);
      logger.info(`Saved crime data batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(crimeData.length / batchSize)}`);
    }

    logger.info(`✅ Generated ${crimeData.length} crime incidents`);
  }

  // Import from CSV file
  async importFromCSV(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => {
          logger.info(`📄 Loaded ${results.length} records from CSV`);
          resolve(results);
        })
        .on('error', reject);
    });
  }

  // Quick setup with sample data
  async quickSetup() {
    logger.info('🚀 Quick setup with sample data...');

    try {
      await this.connectDB();

      // Clear existing data
      await Neighborhood.deleteMany({});
      await CrimeData.deleteMany({});

      // Create sample neighborhoods
      const sampleNeighborhoods = this.createSampleNeighborhoods();
      const savedNeighborhoods = await Neighborhood.insertMany(sampleNeighborhoods);

      logger.info(`✅ Created ${savedNeighborhoods.length} sample neighborhoods`);

      // Generate crime data
      await this.generateCrimeData(savedNeighborhoods);

      // Generate embeddings if possible
      if (process.env.GOOGLE_CLOUD_PROJECT_ID) {
        await this.generateEmbeddings();
      }

      logger.info('🎉 Quick setup completed!');

      return {
        neighborhoods: savedNeighborhoods.length,
        success: true
      };

    } catch (error) {
      logger.error('❌ Quick setup failed:', error);
      throw error;
    }
  }

  // Create sample neighborhoods for testing
  createSampleNeighborhoods() {
    return [
      {
        name: "SoHo",
        borough: "Manhattan",
        coordinates: { lat: 40.7230, lng: -74.0030 },
        demographics: {
          population: 15000,
          medianAge: 35,
          medianIncome: 120000,
          educationLevel: { highSchool: 95, bachelors: 75, graduate: 45 },
          ethnicComposition: { white: 70, black: 8, hispanic: 12, asian: 8, other: 2 }
        },
        housing: {
          avgRent: 5500,
          avgSalePrice: 2500000,
          rentalAvailability: 15,
          homeOwnershipRate: 25,
          pricePerSqFt: 1800
        },
        safety: {
          crimeRate: 2.1,
          safetyScore: 8.5,
          crimeTypes: { violent: 5, property: 15, drug: 3, other: 7 }
        },
        amenities: {
          restaurants: 150,
          schools: 8,
          parks: 3,
          transitScore: 95,
          walkabilityScore: 98,
          bikeScore: 85,
          groceryStores: 25,
          hospitals: 2
        },
        description: "Trendy neighborhood known for cast-iron architecture, high-end shopping, and art galleries",
        tags: ["trendy", "expensive", "artistic", "shopping", "nightlife"]
      },
      // Add more sample neighborhoods...
    ];
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';

  const ingestionService = new DataIngestionService();

  try {
    switch (command) {
      case 'full':
        logger.info('Starting full data ingestion from NYC Open Data...');
        await ingestionService.ingestAllData();
        break;

      case 'quick':
        logger.info('Starting quick setup with sample data...');
        await ingestionService.quickSetup();
        break;

      case 'csv':
        const csvFile = args[1];
        if (!csvFile) {
          logger.error('Please provide CSV file path');
          process.exit(1);
        }
        logger.info(`Importing from CSV: ${csvFile}`);
        const csvData = await ingestionService.importFromCSV(csvFile);
        // Process CSV data...
        break;

      case 'embeddings':
        logger.info('Generating embeddings only...');
        await ingestionService.connectDB();
        await ingestionService.generateEmbeddings();
        break;

      default:
        console.log(`
🏙️  City Insights AI - Data Ingestion Tool

Usage: node dataIngestion.js <command>

Commands:
  full        - Full ingestion from NYC Open Data APIs
  quick       - Quick setup with sample data (recommended for testing)
  csv <file>  - Import from CSV file
  embeddings  - Generate embeddings for existing neighborhoods
  help        - Show this help message

Examples:
  node dataIngestion.js quick
  node dataIngestion.js full
  node dataIngestion.js csv ./data/neighborhoods.csv
  node dataIngestion.js embeddings

Environment variables required:
  MONGODB_URI              - MongoDB connection string
  GOOGLE_CLOUD_PROJECT_ID  - For AI embeddings (optional)
  NYC_OPEN_DATA_API_KEY    - For full ingestion (optional)
        `);
        break;
    }
  } catch (error) {
    logger.error('Script failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Export for use as module
module.exports = DataIngestionService;

// Run if called directly
if (require.main === module) {
  main();
}