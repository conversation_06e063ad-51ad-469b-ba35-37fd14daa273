const mongoose = require('mongoose');
const Neighborhood = require('../src/models/Neighborhood');
const logger = require('../src/utils/logger');

// Sample boundary data for Cape Town suburbs (simplified polygons)
const boundaryData = {
  "Camps Bay": {
    type: "Polygon",
    coordinates: [[
      [18.3700, -33.9500], // lng, lat format for GeoJSON
      [18.3800, -33.9500],
      [18.3850, -33.9550],
      [18.3800, -33.9600],
      [18.3750, -33.9620],
      [18.3700, -33.9600],
      [18.3650, -33.9550],
      [18.3700, -33.9500]  // Close the polygon
    ]]
  },
  "Sea Point": {
    type: "Polygon", 
    coordinates: [[
      [18.3750, -33.9200],
      [18.3900, -33.9200],
      [18.3950, -33.9300],
      [18.3900, -33.9350],
      [18.3800, -33.9380],
      [18.3750, -33.9350],
      [18.3700, -33.9300],
      [18.3750, -33.9200]
    ]]
  },
  "Green Point": {
    type: "Polygon",
    coordinates: [[
      [18.4000, -33.9100],
      [18.4150, -33.9100],
      [18.4200, -33.9200],
      [18.4150, -33.9250],
      [18.4100, -33.9280],
      [18.4000, -33.9250],
      [18.3950, -33.9200],
      [18.4000, -33.9100]
    ]]
  },
  "Clifton": {
    type: "Polygon",
    coordinates: [[
      [18.3650, -33.9400],
      [18.3750, -33.9400],
      [18.3800, -33.9480],
      [18.3750, -33.9520],
      [18.3700, -33.9540],
      [18.3650, -33.9520],
      [18.3600, -33.9480],
      [18.3650, -33.9400]
    ]]
  },
  "Constantia": {
    type: "Polygon",
    coordinates: [[
      [18.4200, -34.0200],
      [18.4400, -34.0200],
      [18.4500, -34.0350],
      [18.4450, -34.0450],
      [18.4350, -34.0500],
      [18.4200, -34.0450],
      [18.4100, -34.0350],
      [18.4200, -34.0200]
    ]]
  },
  "Newlands": {
    type: "Polygon",
    coordinates: [[
      [18.4600, -33.9800],
      [18.4800, -33.9800],
      [18.4900, -33.9950],
      [18.4850, -34.0050],
      [18.4750, -34.0100],
      [18.4600, -34.0050],
      [18.4500, -33.9950],
      [18.4600, -33.9800]
    ]]
  },
  "Rondebosch": {
    type: "Polygon",
    coordinates: [[
      [18.4700, -33.9600],
      [18.4900, -33.9600],
      [18.5000, -33.9750],
      [18.4950, -33.9850],
      [18.4850, -33.9900],
      [18.4700, -33.9850],
      [18.4600, -33.9750],
      [18.4700, -33.9600]
    ]]
  },
  "Observatory": {
    type: "Polygon",
    coordinates: [[
      [18.4800, -33.9300],
      [18.5000, -33.9300],
      [18.5100, -33.9450],
      [18.5050, -33.9550],
      [18.4950, -33.9600],
      [18.4800, -33.9550],
      [18.4700, -33.9450],
      [18.4800, -33.9300]
    ]]
  },
  "Woodstock": {
    type: "Polygon",
    coordinates: [[
      [18.4500, -33.9200],
      [18.4700, -33.9200],
      [18.4800, -33.9350],
      [18.4750, -33.9450],
      [18.4650, -33.9500],
      [18.4500, -33.9450],
      [18.4400, -33.9350],
      [18.4500, -33.9200]
    ]]
  },
  "Salt River": {
    type: "Polygon",
    coordinates: [[
      [18.4700, -33.9350],
      [18.4900, -33.9350],
      [18.5000, -33.9500],
      [18.4950, -33.9600],
      [18.4850, -33.9650],
      [18.4700, -33.9600],
      [18.4600, -33.9500],
      [18.4700, -33.9350]
    ]]
  }
};

async function addBoundaryData() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/city-insights';
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    let updatedCount = 0;

    for (const [suburbName, boundary] of Object.entries(boundaryData)) {
      try {
        const result = await Neighborhood.updateOne(
          { name: suburbName },
          { 
            $set: { 
              boundary: boundary,
              lastUpdated: new Date()
            }
          }
        );

        if (result.matchedCount > 0) {
          logger.info(`✅ Updated boundary for ${suburbName}`);
          updatedCount++;
        } else {
          logger.warn(`⚠️  No neighborhood found with name: ${suburbName}`);
        }
      } catch (error) {
        logger.error(`❌ Error updating ${suburbName}:`, error.message);
      }
    }

    logger.info(`🎉 Successfully updated boundaries for ${updatedCount} neighborhoods`);

    // Verify the updates
    const neighborhoodsWithBoundaries = await Neighborhood.countDocuments({
      'boundary.coordinates': { $exists: true }
    });
    
    logger.info(`📊 Total neighborhoods with boundary data: ${neighborhoodsWithBoundaries}`);

  } catch (error) {
    logger.error('Error adding boundary data:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  addBoundaryData()
    .then(() => {
      logger.info('Boundary data addition completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { addBoundaryData, boundaryData };
