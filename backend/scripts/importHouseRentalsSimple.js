const { MongoClient } = require('mongodb');
const HouseRental = require('../src/models/HouseRental');
const { connectDB } = require('../src/config/database');
require('dotenv').config();

/**
 * Simple script to import house rental data from the houseRentals database
 */

async function importHouseRentals() {
  let client;
  
  try {
    // Connect to MongoDB
    await connectDB();
    console.log('Connected to MongoDB');

    // Connect to the raw MongoDB client for accessing the houseRentals database
    client = new MongoClient(process.env.MONGODB_URI);
    await client.connect();
    const houseRentalsDB = client.db('houseRentals');

    // Get the raw house rentals data from the properties collection
    const houseRentalsCollection = houseRentalsDB.collection('properties');
    const rawRentals = await houseRentalsCollection.find({}).toArray();
    
    console.log(`Found ${rawRentals.length} rental properties to import`);

    if (rawRentals.length === 0) {
      console.warn('No rental data found in houseRentals.properties collection');
      return;
    }

    // Clear existing data
    await HouseRental.deleteMany({});
    console.log('Cleared existing rental data');

    let imported = 0;
    let skipped = 0;

    for (const rental of rawRentals) {
      try {
        // Transform the raw data to match our schema
        const transformedRental = transformRentalData(rental);
        
        // Create new rental document
        const newRental = new HouseRental(transformedRental);
        await newRental.save();
        
        imported++;
        
        if (imported % 10 === 0) {
          console.log(`Imported ${imported} properties...`);
        }
      } catch (error) {
        console.error(`Error importing rental ${rental._id}:`, error.message);
        skipped++;
      }
    }

    console.log(`✅ Import completed: ${imported} imported, ${skipped} skipped`);

    // Generate some statistics
    const stats = await generateImportStats();
    console.log('Import Statistics:', JSON.stringify(stats, null, 2));

  } catch (error) {
    console.error('Error importing house rentals:', error);
    throw error;
  } finally {
    if (client) {
      await client.close();
    }
  }
}

/**
 * Transform raw rental data to match our schema
 */
function transformRentalData(rental) {
  // Get coordinates for the location
  const getCoordinates = (location) => {
    const locationCoords = {
      'Cape Town City Centre': { lat: -33.9249, lng: 18.4241 },
      'Sea Point': { lat: -33.9248, lng: 18.3917 },
      'Camps Bay': { lat: -33.9489, lng: 18.3758 },
      'Constantia': { lat: -34.0235, lng: 18.4198 },
      'Claremont': { lat: -33.9847, lng: 18.4647 },
      'Woodstock': { lat: -33.9258, lng: 18.4467 },
      'Observatory': { lat: -33.9333, lng: 18.4667 },
      'Gardens': { lat: -33.9333, lng: 18.4167 },
      'Kenilworth': { lat: -33.9667, lng: 18.4833 },
      'Muizenberg': { lat: -34.1047, lng: 18.4669 },
      'Stellenbosch': { lat: -33.9321, lng: 18.8602 },
      'Paarl': { lat: -33.7369, lng: 18.9584 },
      'Somerset West': { lat: -34.0781, lng: 18.8419 },
      'Milnerton': { lat: -33.8569, lng: 18.4956 },
      'Wynberg': { lat: -34.0047, lng: 18.4647 },
      'Foreshore': { lat: -33.9167, lng: 18.4167 },
      'Bantry Bay': { lat: -33.9167, lng: 18.3833 },
      'Fresnaye': { lat: -33.9167, lng: 18.3833 },
      'Bo Kaap': { lat: -33.9167, lng: 18.4167 },
      'De Waterkant': { lat: -33.9167, lng: 18.4167 },
      'Higgovale': { lat: -33.9333, lng: 18.4167 },
      'Mouille Point': { lat: -33.9167, lng: 18.4167 },
      'Diep River': { lat: -34.0333, lng: 18.4833 },
      'Belhar': { lat: -33.8833, lng: 18.6167 },
      'Claremont Upper': { lat: -33.9847, lng: 18.4647 }
    };

    return locationCoords[location] || { lat: -33.9249, lng: 18.4241 };
  };

  // Determine category based on price
  const getCategory = (price) => {
    // Map existing categories to our schema
    if (rental.priceCategory) {
      const categoryMap = {
        'Budget': 'Budget',
        'Moderate': 'Moderate',
        'Expensive': 'Luxury',  // Map Expensive to Luxury
        'Luxury': 'Luxury',
        'Ultra-Luxury': 'Ultra-Luxury'
      };
      return categoryMap[rental.priceCategory] || 'Moderate';
    }

    if (price >= 50000) return 'Ultra-Luxury';
    if (price >= 35000) return 'Luxury';
    if (price >= 20000) return 'Moderate';
    return 'Budget';
  };

  // Determine size based on bedrooms
  const getSize = (bedrooms) => {
    // Map existing sizes to our schema
    if (rental.sizeCategory) {
      const sizeMap = {
        'Compact': 'Compact',
        'Medium': 'Medium',
        'Large': 'Large',
        'Extra Large': 'Extra Large',
        'Studio': 'Compact'  // Map Studio to Compact
      };
      return sizeMap[rental.sizeCategory] || 'Medium';
    }

    if (bedrooms >= 4) return 'Extra Large';
    if (bedrooms >= 3) return 'Large';
    if (bedrooms >= 2) return 'Medium';
    return 'Compact';
  };

  // Extract property type from title
  const getPropertyType = (title) => {
    const titleLower = title.toLowerCase();
    if (titleLower.includes('house')) return 'House';
    if (titleLower.includes('apartment')) return 'Apartment';
    if (titleLower.includes('penthouse')) return 'Penthouse';
    if (titleLower.includes('loft')) return 'Loft';
    if (titleLower.includes('studio')) return 'Studio';
    if (titleLower.includes('townhouse')) return 'Townhouse';
    return 'Apartment';
  };

  const coordinates = getCoordinates(rental.location);
  const category = getCategory(rental.price);
  const size = getSize(rental.bedrooms);
  const propertyType = getPropertyType(rental.title);

  return {
    title: rental.title,
    location: rental.location,
    price: rental.price,
    bedrooms: rental.bedrooms,
    bathrooms: rental.bathrooms || Math.max(1, Math.floor(rental.bedrooms * 0.75)),
    floorSize: rental.floorSize || null,
    parking: rental.parkingSpaces || 0,
    propertyType: propertyType,
    category: category,
    size: size,
    features: rental.features || [],
    amenities: rental.amenities || [],
    description: rental.description || `Beautiful ${rental.bedrooms} bedroom ${propertyType.toLowerCase()} in ${rental.location}`,
    coordinates: coordinates,
    availability: {
      available: true,
      availableFrom: new Date(),
      leaseTerm: '12 months'
    },
    furnished: rental.furnished || 'Unfurnished',
    lastUpdated: new Date(),
    dataSource: 'Cape Town Rental Market 2025'
  };
}

/**
 * Generate import statistics
 */
async function generateImportStats() {
  const [
    totalCount,
    locationStats,
    priceStats,
    bedroomStats
  ] = await Promise.all([
    HouseRental.countDocuments(),
    HouseRental.aggregate([
      { $group: { _id: '$location', count: { $sum: 1 }, avgPrice: { $avg: '$price' } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]),
    HouseRental.aggregate([
      { $group: { _id: null, avgPrice: { $avg: '$price' }, minPrice: { $min: '$price' }, maxPrice: { $max: '$price' } } }
    ]),
    HouseRental.aggregate([
      { $group: { _id: '$bedrooms', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ])
  ]);

  return {
    totalProperties: totalCount,
    topLocations: locationStats.slice(0, 5),
    priceRange: priceStats[0] || {},
    bedroomDistribution: bedroomStats
  };
}

// Run the import
importHouseRentals()
  .then(() => {
    console.log('House rental import completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('House rental import failed:', error);
    process.exit(1);
  });
