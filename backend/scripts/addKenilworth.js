require('dotenv').config();
const mongoose = require('mongoose');
const Neighborhood = require('../src/models/Neighborhood');
const geminiService = require('../src/services/geminiService');
const { logger } = require('../src/utils/logger');

// Kenilworth neighborhood data
const kenilworthData = {
  name: "Kenilworth",
  borough: "Southern Suburbs",
  city: "Cape Town",
  state: "Western Cape",
  coordinates: { lat: -33.996753, lng: 18.473102 },
  demographics: {
    population: 12500,
    medianAge: 41,
    medianIncome: 520000,
    ethnicComposition: {
      white: 58, black: 15, coloured: 22, indian: 4, other: 1
    },
    educationLevel: {
      matric: 92, diploma: 48, degree: 62
    }
  },
  housing: {
    avgRent: 26000,
    rentalRange: { min: 12000, max: 55000 },
    rentByBedroom: {
      studio: { 
        min: 12000, max: 16000, avg: 14000,
        typical: "Modern studio in secure complex with garden access",
        features: ["Garden access", "Security", "Parking", "Pool"]
      },
      oneBed: { 
        min: 15000, max: 25000, avg: 20000,
        typical: "Apartment in leafy complex or cottage with garden",
        features: ["Garden views", "Security", "Pool", "Pet-friendly"]
      },
      twoBed: { 
        min: 20000, max: 35000, avg: 27500,
        typical: "Spacious apartment or cottage with private garden",
        features: ["Private garden", "2 bathrooms", "Security", "Parking"]
      },
      threeBed: { 
        min: 28000, max: 45000, avg: 36500,
        typical: "Family house or large apartment with garden and study",
        features: ["Large garden", "Study", "Family-friendly", "Garage"]
      },
      fourBed: { 
        min: 40000, max: 55000, avg: 47500,
        typical: "Large family house with extensive gardens and pool",
        features: ["Large garden", "Pool", "Multiple bathrooms", "Staff quarters"]
      }
    },
    avgSalePrice: 4200000,
    rentalAvailability: 16,
    homeOwnershipRate: 75,
    pricePerSqM: 38000,
    rentalYield: 6.8,
    propertyTypes: {
      houses: 70, apartments: 25, cottages: 5
    },
    utilities: {
      included: ["Water", "Refuse"],
      excluded: ["Electricity", "Internet"],
      avgUtilityCost: 1900
    }
  },
  safety: {
    crimeRate: 2.2,
    safetyScore: 8.1,
    crimeTypes: { violent: 3, property: 16, drug: 2, other: 6 },
    securityFeatures: ["Neighbourhood watch", "Security estates", "CCTV", "Access control"]
  },
  amenities: {
    restaurants: 22,
    schools: 8,
    parks: 6,
    transitScore: 72,
    walkabilityScore: 78,
    bikeScore: 85,
    groceryStores: 12,
    hospitals: 1,
    beaches: 0,
    entertainment: ["Kenilworth Race Course", "Local pubs", "Community centers"],
    shopping: ["Cavendish Square nearby", "Local shops", "Farmers markets"]
  },
  description: "Established residential suburb known for its leafy streets, family-friendly atmosphere, and proximity to Cavendish Square. Popular with families and professionals seeking suburban tranquility.",
  tags: ["family-friendly", "leafy", "suburban", "established", "race course", "quiet residential"],
  climate: {
    avgTemp: 18,
    rainyDays: 75,
    windyDays: 95,
    bestMonths: ["Oct", "Nov", "Dec", "Jan", "Feb", "Mar"]
  },
  transport: {
    uberAvg: 20,
    publicTransport: ["Train line", "Bus routes", "Taxi routes"],
    parking: "Generally excellent availability"
  },
  dataSource: "Cape Town Real Estate & Crime Data 2024",
  affordabilityCategory: "Expensive"
};

async function addKenilworth() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    logger.info('Connected to MongoDB');

    // Check if Kenilworth already exists
    const existingKenilworth = await Neighborhood.findOne({ name: "Kenilworth" });
    
    if (existingKenilworth) {
      logger.info('Kenilworth already exists, updating coordinates...');

      // Update the coordinates
      existingKenilworth.coordinates = kenilworthData.coordinates;
      await existingKenilworth.save();

      logger.info(`Updated Kenilworth coordinates to: ${kenilworthData.coordinates.lat}, ${kenilworthData.coordinates.lng}`);
    } else {
      logger.info('Adding new Kenilworth neighborhood...');

      // Add required fields
      kenilworthData.lastUpdated = new Date();
      kenilworthData.dataSource = 'Cape Town Real Estate & Crime Data 2024';
      kenilworthData.affordabilityCategory = 'Expensive'; // Based on avgRent of 26000

      // Generate vector embedding
      logger.info('Generating embedding for Kenilworth...');
      const embedding = await geminiService.generateNeighborhoodEmbedding(kenilworthData);
      kenilworthData.vectorEmbedding = embedding;

      // Create new neighborhood
      const kenilworth = new Neighborhood(kenilworthData);
      await kenilworth.save();

      logger.info(`Added Kenilworth with coordinates: ${kenilworthData.coordinates.lat}, ${kenilworthData.coordinates.lng}`);
    }

    // Verify the data
    const savedKenilworth = await Neighborhood.findOne({ name: "Kenilworth" });
    logger.info('Kenilworth data:', {
      name: savedKenilworth.name,
      coordinates: savedKenilworth.coordinates,
      borough: savedKenilworth.borough
    });

    logger.info('Kenilworth successfully added/updated!');
    
  } catch (error) {
    logger.error('Error adding Kenilworth:', error);
  } finally {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

// Run the script
addKenilworth();
