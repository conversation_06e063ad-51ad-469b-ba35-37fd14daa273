const { MongoClient } = require('mongodb');
const { logger } = require('../src/utils/logger');
require('dotenv').config();

async function checkCollections() {
  let client;
  
  try {
    // Connect to MongoDB
    client = new MongoClient(process.env.MONGODB_URI);
    await client.connect();
    const db = client.db();
    
    logger.info('Connected to MongoDB');
    logger.info(`Database: ${db.databaseName}`);
    
    // List all collections
    const collections = await db.listCollections().toArray();
    logger.info(`Found ${collections.length} collections:`);
    
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      logger.info(`  - ${collection.name}: ${count} documents`);
      
      // If it's houseRentals, show a sample
      if (collection.name === 'houseRentals' && count > 0) {
        const sample = await db.collection(collection.name).findOne();
        logger.info('Sample houseRentals document:');
        console.log(JSON.stringify(sample, null, 2));
      }
    }
    
    // Check if houseRentals exists and has data
    const houseRentalsCount = await db.collection('houseRentals').countDocuments();
    if (houseRentalsCount === 0) {
      logger.warn('houseRentals collection is empty or does not exist');
      logger.info('You may need to:');
      logger.info('1. Import your rental data into the houseRentals collection');
      logger.info('2. Check if the data is in a different collection name');
      logger.info('3. Verify your MongoDB connection string points to the correct database');
    }
    
  } catch (error) {
    logger.error('Error checking collections:', error);
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// Run the check
checkCollections()
  .then(() => {
    logger.info('Collection check completed');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Collection check failed:', error);
    process.exit(1);
  });
