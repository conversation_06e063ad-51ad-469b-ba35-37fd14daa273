const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const Neighborhood = require('../src/models/Neighborhood');
const CrimeData = require('../src/models/CrimeData');
const geminiService = require('../src/services/geminiService');
const { logger } = require('../src/utils/logger');

// Sample neighborhood data for NYC
const sampleNeighborhoods = [
  {
    name: "SoHo",
    borough: "Manhattan",
    coordinates: { lat: 40.7230, lng: -74.0030 },
    demographics: {
      population: 15000,
      medianAge: 35,
      medianIncome: 120000,
      educationLevel: { highSchool: 95, bachelors: 75, graduate: 45 },
      ethnicComposition: { white: 70, black: 8, hispanic: 12, asian: 8, other: 2 }
    },
    housing: {
      avgRent: 5500,
      avgSalePrice: 2500000,
      rentalAvailability: 15,
      homeOwnershipRate: 25,
      pricePerSqFt: 1800
    },
    safety: {
      crimeRate: 2.1,
      safetyScore: 8.5,
      crimeTypes: { violent: 5, property: 15, drug: 3, other: 7 }
    },
    amenities: {
      restaurants: 150,
      schools: 8,
      parks: 3,
      transitScore: 95,
      walkabilityScore: 98,
      bikeScore: 85,
      groceryStores: 25,
      hospitals: 2
    },
    description: "Trendy neighborhood known for cast-iron architecture, high-end shopping, and art galleries",
    tags: ["trendy", "expensive", "artistic", "shopping", "nightlife"]
  },
  {
    name: "Greenwich Village",
    borough: "Manhattan",
    coordinates: { lat: 40.7335, lng: -74.0027 },
    demographics: {
      population: 22000,
      medianAge: 38,
      medianIncome: 110000,
      educationLevel: { highSchool: 92, bachelors: 78, graduate: 52 },
      ethnicComposition: { white: 75, black: 6, hispanic: 10, asian: 7, other: 2 }
    },
    housing: {
      avgRent: 4800,
      avgSalePrice: 2200000,
      rentalAvailability: 18,
      homeOwnershipRate: 35,
      pricePerSqFt: 1600
    },
    safety: {
      crimeRate: 1.8,
      safetyScore: 9.0,
      crimeTypes: { violent: 3, property: 12, drug: 2, other: 5 }
    },
    amenities: {
      restaurants: 200,
      schools: 12,
      parks: 5,
      transitScore: 92,
      walkabilityScore: 96,
      bikeScore: 88,
      groceryStores: 30,
      hospitals: 3
    },
    description: "Historic bohemian neighborhood with tree-lined streets, cafes, and cultural venues",
    tags: ["historic", "bohemian", "cultural", "walkable", "expensive"]
  },
  {
    name: "Williamsburg",
    borough: "Brooklyn",
    coordinates: { lat: 40.7081, lng: -73.9571 },
    demographics: {
      population: 35000,
      medianAge: 32,
      medianIncome: 85000,
      educationLevel: { highSchool: 88, bachelors: 65, graduate: 35 },
      ethnicComposition: { white: 60, black: 12, hispanic: 20, asian: 6, other: 2 }
    },
    housing: {
      avgRent: 3800,
      avgSalePrice: 1400000,
      rentalAvailability: 25,
      homeOwnershipRate: 40,
      pricePerSqFt: 1200
    },
    safety: {
      crimeRate: 2.8,
      safetyScore: 7.5,
      crimeTypes: { violent: 8, property: 22, drug: 5, other: 10 }
    },
    amenities: {
      restaurants: 180,
      schools: 15,
      parks: 8,
      transitScore: 85,
      walkabilityScore: 90,
      bikeScore: 92,
      groceryStores: 35,
      hospitals: 2
    },
    description: "Hip neighborhood with artisanal everything, waterfront views, and young professional crowd",
    tags: ["hip", "young professionals", "artisanal", "waterfront", "trendy"]
  },
  {
    name: "Park Slope",
    borough: "Brooklyn",
    coordinates: { lat: 40.6782, lng: -73.9776 },
    demographics: {
      population: 28000,
      medianAge: 40,
      medianIncome: 95000,
      educationLevel: { highSchool: 95, bachelors: 82, graduate: 58 },
      ethnicComposition: { white: 78, black: 8, hispanic: 8, asian: 4, other: 2 }
    },
    housing: {
      avgRent: 4200,
      avgSalePrice: 1800000,
      rentalAvailability: 20,
      homeOwnershipRate: 55,
      pricePerSqFt: 1400
    },
    safety: {
      crimeRate: 1.5,
      safetyScore: 9.2,
      crimeTypes: { violent: 2, property: 8, drug: 1, other: 4 }
    },
    amenities: {
      restaurants: 120,
      schools: 20,
      parks: 12,
      transitScore: 88,
      walkabilityScore: 94,
      bikeScore: 85,
      groceryStores: 40,
      hospitals: 1
    },
    description: "Family-friendly neighborhood with excellent schools, Prospect Park access, and brownstones",
    tags: ["family-friendly", "schools", "parks", "safe", "brownstones"]
  },
  {
    name: "Astoria",
    borough: "Queens",
    coordinates: { lat: 40.7698, lng: -73.9442 },
    demographics: {
      population: 45000,
      medianAge: 35,
      medianIncome: 65000,
      educationLevel: { highSchool: 82, bachelors: 55, graduate: 28 },
      ethnicComposition: { white: 45, black: 15, hispanic: 25, asian: 12, other: 3 }
    },
    housing: {
      avgRent: 2800,
      avgSalePrice: 850000,
      rentalAvailability: 35,
      homeOwnershipRate: 45,
      pricePerSqFt: 800
    },
    safety: {
      crimeRate: 3.2,
      safetyScore: 7.0,
      crimeTypes: { violent: 12, property: 28, drug: 8, other: 15 }
    },
    amenities: {
      restaurants: 250,
      schools: 18,
      parks: 10,
      transitScore: 82,
      walkabilityScore: 85,
      bikeScore: 75,
      groceryStores: 45,
      hospitals: 2
    },
    description: "Diverse neighborhood known for authentic international cuisine and affordable housing",
    tags: ["diverse", "affordable", "international food", "multicultural"]
  },
  {
    name: "Upper East Side",
    borough: "Manhattan",
    coordinates: { lat: 40.7736, lng: -73.9566 },
    demographics: {
      population: 55000,
      medianAge: 42,
      medianIncome: 130000,
      educationLevel: { highSchool: 96, bachelors: 85, graduate: 62 },
      ethnicComposition: { white: 82, black: 4, hispanic: 8, asian: 5, other: 1 }
    },
    housing: {
      avgRent: 5200,
      avgSalePrice: 2800000,
      rentalAvailability: 12,
      homeOwnershipRate: 30,
      pricePerSqFt: 1900
    },
    safety: {
      crimeRate: 1.2,
      safetyScore: 9.5,
      crimeTypes: { violent: 1, property: 6, drug: 1, other: 2 }
    },
    amenities: {
      restaurants: 180,
      schools: 25,
      parks: 8,
      transitScore: 90,
      walkabilityScore: 92,
      bikeScore: 70,
      groceryStores: 35,
      hospitals: 5
    },
    description: "Upscale neighborhood with museums, luxury shopping, and Central Park access",
    tags: ["upscale", "museums", "luxury", "safe", "central park"]
  }
];

async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    logger.info('Connected to MongoDB');
  } catch (error) {
    logger.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

async function generateEmbeddings() {
  logger.info('Generating embeddings for neighborhoods...');
  
  const neighborhoods = await Neighborhood.find({ vectorEmbedding: { $exists: false } });
  
  for (const neighborhood of neighborhoods) {
    try {
      logger.info(`Generating embedding for ${neighborhood.name}...`);
      const embedding = await geminiService.generateNeighborhoodEmbedding(neighborhood);
      
      neighborhood.vectorEmbedding = embedding;
      await neighborhood.save();
      
      logger.info(`✓ Generated embedding for ${neighborhood.name}`);
    } catch (error) {
      logger.error(`Failed to generate embedding for ${neighborhood.name}:`, error);
    }
  }
}

async function importNeighborhoods() {
  logger.info('Importing sample neighborhoods...');

  try {
    // Clear existing data
    await Neighborhood.deleteMany({});
    logger.info('Cleared existing neighborhood data');

    // Generate embeddings for each neighborhood before inserting
    const neighborhoodsWithEmbeddings = [];

    for (const neighborhoodData of sampleNeighborhoods) {
      try {
        logger.info(`Generating embedding for ${neighborhoodData.name}...`);
        const embedding = await geminiService.generateNeighborhoodEmbedding(neighborhoodData);

        neighborhoodsWithEmbeddings.push({
          ...neighborhoodData,
          vectorEmbedding: embedding
        });

        logger.info(`✓ Generated embedding for ${neighborhoodData.name} (${embedding.length} dimensions)`);
      } catch (error) {
        logger.error(`Failed to generate embedding for ${neighborhoodData.name}:`, error);
        // Skip this neighborhood if embedding generation fails
        continue;
      }
    }

    // Insert neighborhoods with embeddings
    const neighborhoods = await Neighborhood.insertMany(neighborhoodsWithEmbeddings);
    logger.info(`✓ Imported ${neighborhoods.length} neighborhoods with embeddings`);

    return neighborhoods;
  } catch (error) {
    logger.error('Error importing neighborhoods:', error);
    throw error;
  }
}

async function generateSampleCrimeData() {
  logger.info('Generating sample crime data...');
  
  const neighborhoods = await Neighborhood.find();
  const crimeTypes = ['ASSAULT', 'BURGLARY', 'ROBBERY', 'THEFT', 'VANDALISM', 'DRUG_OFFENSE'];
  const categories = ['VIOLENT', 'PROPERTY', 'DRUG', 'OTHER'];
  const timeOfDay = ['MORNING', 'AFTERNOON', 'EVENING', 'NIGHT'];
  const daysOfWeek = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'];
  
  const crimeData = [];
  
  for (const neighborhood of neighborhoods) {
    // Generate 50-200 crime incidents per neighborhood over the last year
    const numIncidents = Math.floor(Math.random() * 150) + 50;
    
    for (let i = 0; i < numIncidents; i++) {
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 365));
      
      const incidentType = crimeTypes[Math.floor(Math.random() * crimeTypes.length)];
      let category;
      
      if (['ASSAULT', 'ROBBERY'].includes(incidentType)) category = 'VIOLENT';
      else if (['BURGLARY', 'THEFT', 'VANDALISM'].includes(incidentType)) category = 'PROPERTY';
      else if (incidentType === 'DRUG_OFFENSE') category = 'DRUG';
      else category = 'OTHER';
      
      // Add some random variation to coordinates
      const lat = neighborhood.coordinates.lat + (Math.random() - 0.5) * 0.01;
      const lng = neighborhood.coordinates.lng + (Math.random() - 0.5) * 0.01;
      
      crimeData.push({
        neighborhood: neighborhood.name,
        borough: neighborhood.borough,
        incidentType,
        category,
        date,
        coordinates: { lat, lng },
        severity: Math.random() > 0.7 ? 'HIGH' : Math.random() > 0.4 ? 'MEDIUM' : 'LOW',
        timeOfDay: timeOfDay[Math.floor(Math.random() * timeOfDay.length)],
        dayOfWeek: daysOfWeek[Math.floor(Math.random() * daysOfWeek.length)],
        month: date.getMonth() + 1,
        year: date.getFullYear(),
        resolved: Math.random() > 0.3
      });
    }
  }
  
  try {
    await CrimeData.deleteMany({});
    await CrimeData.insertMany(crimeData);
    logger.info(`✓ Generated ${crimeData.length} crime incidents`);
  } catch (error) {
    logger.error('Error generating crime data:', error);
    throw error;
  }
}

async function main() {
  try {
    await connectDB();

    logger.info('🚀 Starting data import process...');

    // Check if Google Cloud is configured for embeddings
    if (!process.env.GOOGLE_CLOUD_PROJECT_ID) {
      logger.error('❌ GOOGLE_CLOUD_PROJECT_ID environment variable is required for embedding generation');
      process.exit(1);
    }

    // Import neighborhoods (with embeddings)
    await importNeighborhoods();

    // Generate crime data
    await generateSampleCrimeData();

    logger.info('✅ Data import completed successfully!');

  } catch (error) {
    logger.error('❌ Data import failed:', error);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

// Run the import
if (require.main === module) {
  main();
}

module.exports = { importNeighborhoods, generateSampleCrimeData, generateEmbeddings };
