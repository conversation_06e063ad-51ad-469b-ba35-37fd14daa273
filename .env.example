# Environment
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/cityinsights
MONGODB_DB_NAME=cityinsights

# For local development with Docker
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=password

# Google Cloud Platform
GOOGLE_CLOUD_PROJECT_ID=your-project-id
VERTEX_AI_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=./credentials/service-account.json

# Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# JWT Secret (for future auth implementation)
JWT_SECRET=your-super-secret-jwt-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Data Sources
NYC_OPEN_DATA_API_KEY=your-nyc-open-data-api-key

# External Services (Optional)
SENTRY_DSN=your-sentry-dsn-for-error-tracking
GOOGLE_ANALYTICS_ID=your-google-analytics-id
