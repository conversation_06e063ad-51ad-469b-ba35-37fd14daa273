# Google Cloud Build configuration for City Insights AI
steps:
  # Build backend Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/city-insights-backend:$COMMIT_SHA'
      - '-t'
      - 'gcr.io/$PROJECT_ID/city-insights-backend:latest'
      - './backend'
    id: 'build-backend'

  # Build frontend Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/city-insights-frontend:$COMMIT_SHA'
      - '-t'
      - 'gcr.io/$PROJECT_ID/city-insights-frontend:latest'
      - './frontend'
    id: 'build-frontend'

  # Push backend image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/city-insights-backend:$COMMIT_SHA'
    id: 'push-backend'
    waitFor: ['build-backend']

  # Push frontend image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/city-insights-frontend:$COMMIT_SHA'
    id: 'push-frontend'
    waitFor: ['build-frontend']

  # Deploy backend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'city-insights-backend'
      - '--image'
      - 'gcr.io/$PROJECT_ID/city-insights-backend:$COMMIT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--memory'
      - '2Gi'
      - '--cpu'
      - '2'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - 'NODE_ENV=production,GOOGLE_CLOUD_PROJECT_ID=$PROJECT_ID'
      - '--set-secrets'
      - 'MONGODB_URI=mongodb-uri:latest,MONGODB_DB_NAME=mongodb-db-name:latest,GEMINI_API_KEY=gemini-api-key:latest'
    id: 'deploy-backend'
    waitFor: ['push-backend']

  # Deploy frontend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'city-insights-frontend'
      - '--image'
      - 'gcr.io/$PROJECT_ID/city-insights-frontend:$COMMIT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--memory'
      - '512Mi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '5'
      - '--set-env-vars'
      - 'VITE_API_URL=https://city-insights-backend-91202037874.us-central1.run.app/api'
    id: 'deploy-frontend'
    waitFor: ['push-frontend', 'deploy-backend']

# Store images in Google Container Registry
images:
  - 'gcr.io/$PROJECT_ID/city-insights-backend:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/city-insights-frontend:$COMMIT_SHA'

# Build options
options:
  machineType: 'E2_HIGHCPU_8'
  substitutionOption: 'ALLOW_LOOSE'
  logging: 'CLOUD_LOGGING_ONLY'

# Substitutions
substitutions:
  _REGION: 'us-central1'
  _SERVICE_NAME_BACKEND: 'city-insights-backend'
  _SERVICE_NAME_FRONTEND: 'city-insights-frontend'

# Timeout
timeout: '1200s'
